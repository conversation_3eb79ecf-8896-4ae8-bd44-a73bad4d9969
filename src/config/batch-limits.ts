/**
 * Batch generation limits configuration
 */

export interface BatchLimitsConfig {
  /** Maximum certificates per batch */
  maxCertificatesPerBatch: number;
  /** Large batch warning threshold */
  warningThreshold: number;
  /** Maximum concurrency */
  maxConcurrency: number;
  /** Task timeout in minutes */
  timeoutMinutes: number;
  /** Maximum concurrent tasks */
  maxConcurrentTasks: number;
  /** Completed task keep-alive time in minutes */
  completedTaskKeepAliveMinutes: number;
}

/**
 * Default batch limits configuration
 * Optimized for serverless environments like Vercel
 */
export const DEFAULT_BATCH_LIMITS: BatchLimitsConfig = {
  maxCertificatesPerBatch: 20, // Reduced for serverless
  warningThreshold: 15,
  maxConcurrency: 3, // Reduced to prevent memory issues
  timeoutMinutes: 1, // Vercel Pro limit is 60 seconds
  maxConcurrentTasks: 2, // Reduced for serverless
  completedTaskKeepAliveMinutes: 30 // Reduced cleanup time
};

/**
 * Get batch limits configuration based on environment
 */
export function getBatchLimitsConfig(): BatchLimitsConfig {
  // Detect serverless environment
  const isServerless = process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.NETLIFY;

  // Use more conservative limits in serverless environments
  const config = { ...DEFAULT_BATCH_LIMITS };

  if (isServerless) {
    // Further reduce limits for serverless
    config.maxCertificatesPerBatch = Math.min(config.maxCertificatesPerBatch, 15);
    config.maxConcurrency = Math.min(config.maxConcurrency, 2);
    config.timeoutMinutes = Math.min(config.timeoutMinutes, 1);
    config.maxConcurrentTasks = Math.min(config.maxConcurrentTasks, 1);
  }

  // Override configuration from environment variables
  if (process.env.MAX_CERTIFICATES_PER_BATCH) {
    config.maxCertificatesPerBatch = parseInt(process.env.MAX_CERTIFICATES_PER_BATCH, 10);
  }
  
  if (process.env.BATCH_WARNING_THRESHOLD) {
    config.warningThreshold = parseInt(process.env.BATCH_WARNING_THRESHOLD, 10);
  }
  
  if (process.env.MAX_BATCH_CONCURRENCY) {
    config.maxConcurrency = parseInt(process.env.MAX_BATCH_CONCURRENCY, 10);
  }
  
  if (process.env.BATCH_TIMEOUT_MINUTES) {
    config.timeoutMinutes = parseInt(process.env.BATCH_TIMEOUT_MINUTES, 10);
  }
  
  if (process.env.MAX_CONCURRENT_TASKS) {
    config.maxConcurrentTasks = parseInt(process.env.MAX_CONCURRENT_TASKS, 10);
  }
  
  if (process.env.COMPLETED_TASK_KEEP_ALIVE_MINUTES) {
    config.completedTaskKeepAliveMinutes = parseInt(process.env.COMPLETED_TASK_KEEP_ALIVE_MINUTES, 10);
  }
  
  return config;
}

/**
 * Get user-friendly description of batch limits
 */
export function getBatchLimitsDescription(config: BatchLimitsConfig): string {
  return `Maximum ${config.maxCertificatesPerBatch} certificates per batch. For larger quantities, please split your data into multiple files.`;
}

/**
 * Check if batch size exceeds limits
 */
export function validateBatchSize(count: number, config: BatchLimitsConfig): {
  valid: boolean;
  warning?: string;
  error?: string;
} {
  if (count <= 0) {
    return {
      valid: false,
      error: 'Certificate count must be greater than 0'
    };
  }

  if (count > config.maxCertificatesPerBatch) {
    return {
      valid: false,
      error: `File contains ${count} records, but maximum allowed is ${config.maxCertificatesPerBatch} certificates per batch. Please reduce the number of records or split into multiple files.`
    };
  }

  if (count > config.warningThreshold) {
    return {
      valid: true,
      warning: `You are generating ${count} certificates, this may take a long time. Recommend processing in batches for better experience.`
    };
  }

  return { valid: true };
}

/**
 * Calculate recommended concurrency
 */
export function calculateRecommendedConcurrency(certificateCount: number, config: BatchLimitsConfig): number {
  return Math.min(
    config.maxConcurrency,
    Math.max(2, Math.ceil(certificateCount / 50))
  );
}

/**
 * Estimate processing time
 */
export function estimateProcessingTime(certificateCount: number): {
  estimatedSeconds: number;
  estimatedMinutes: number;
  displayText: string;
} {
  // Based on test results, average ~100ms per certificate
  const avgTimePerCertificate = 100; // ms
  const estimatedMs = certificateCount * avgTimePerCertificate;
  const estimatedSeconds = Math.ceil(estimatedMs / 1000);
  const estimatedMinutes = Math.ceil(estimatedSeconds / 60);

  let displayText: string;
  if (estimatedSeconds < 60) {
    displayText = `About ${estimatedSeconds} seconds`;
  } else if (estimatedMinutes < 60) {
    displayText = `About ${estimatedMinutes} minutes`;
  } else {
    const hours = Math.ceil(estimatedMinutes / 60);
    displayText = `About ${hours} hours`;
  }

  return {
    estimatedSeconds,
    estimatedMinutes,
    displayText
  };
}
