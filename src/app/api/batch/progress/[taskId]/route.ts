import { NextRequest, NextResponse } from 'next/server';
import { BatchTaskManager } from '@/lib/batch-task-manager';

interface RouteParams {
  params: {
    taskId: string;
  };
}

/**
 * Get batch generation task progress
 * GET /api/batch/progress/[taskId]
 */
export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID不能为空' },
        { status: 400 }
      );
    }

    console.log(`📊 Getting progress for task: ${taskId}`);

    // 获取任务进度
    const progress = BatchTaskManager.getTaskProgress(taskId);

    if (!progress) {
      console.warn(`❌ Task ${taskId} not found in progress API`);
      return NextResponse.json(
        { error: 'Task does not exist or has expired' },
        { status: 404 }
      );
    }

    console.log(`📊 Task status: ${progress.status}, progress: ${progress.completedCount + progress.failedCount}/${progress.totalCount}`);

    // Return progress information, ensure data structure is correct
    return NextResponse.json({
      success: true,
      taskId: progress.taskId,
      status: progress.status,
      totalCount: progress.totalCount,
      completedCount: progress.completedCount,
      failedCount: progress.failedCount,
      progress: progress.progress,
      errors: progress.errors || [],
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      currentItem: progress.currentItem
    });

  } catch (error) {
    console.error(`❌ Get progress error for task ${params.taskId}:`, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get progress',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Cancel batch generation task
 * DELETE /api/batch/progress/[taskId]
 */
export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID不能为空' },
        { status: 400 }
      );
    }

    console.log(`🛑 Cancelling task: ${taskId}`);

    // 取消任务
    const cancelled = BatchTaskManager.cancelTask(taskId);

    if (!cancelled) {
      return NextResponse.json(
        { error: '任务无法取消（可能已完成或不存在）' },
        { status: 400 }
      );
    }

    console.log(`✅ Task cancelled successfully: ${taskId}`);

    return NextResponse.json({
      success: true,
      message: '任务已取消'
    });

  } catch (error) {
    console.error(`❌ Cancel task error for ${params.taskId}:`, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to cancel task',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
