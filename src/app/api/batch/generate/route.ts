import { NextRequest, NextResponse } from 'next/server';
import {
  BatchGenerationRequest
} from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { BatchTaskManager } from '@/lib/batch-task-manager';
import { BatchPDFGenerator } from '@/lib/batch-pdf-generator';
import { ZipPackager } from '@/lib/zip-packager';
import { getBatchLimitsConfig, validateBatchSize, calculateRecommendedConcurrency, estimateProcessingTime } from '@/config/batch-limits';
import path from 'path';

/**
 * 批量PDF生成API
 * POST /api/batch/generate
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Batch PDF generation request received');

    const body: BatchGenerationRequest = await request.json();
    
    // 验证请求数据
    if (!body.templateId || !body.certificates || body.certificates.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // 验证模板是否存在
    const template = TemplateManager.getTemplateById(body.templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // 获取批量限制配置
    const batchConfig = getBatchLimitsConfig();

    // 验证批量大小
    const validation = validateBatchSize(body.certificates.length, batchConfig);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // 检查系统当前负载
    const currentTasks = BatchTaskManager.getActiveTasks();
    const totalProcessingCertificates = currentTasks.reduce((sum, task) => sum + task.totalCount, 0);

    if (totalProcessingCertificates > batchConfig.maxConcurrentTasks * batchConfig.maxCertificatesPerBatch) {
      return NextResponse.json(
        { error: 'System load is too high, please try again later' },
        { status: 503 }
      );
    }

    // 大批量警告
    if (validation.warning) {
      console.log(`⚠️  Large batch detected: ${body.certificates.length} certificates. ${validation.warning}`);
    }

    // 估算处理时间
    const timeEstimate = estimateProcessingTime(body.certificates.length);
    console.log(`⏱️  Estimated processing time: ${timeEstimate.displayText}`);

    // 计算推荐并发数
    const recommendedConcurrency = calculateRecommendedConcurrency(body.certificates.length, batchConfig);
    const finalConcurrency = body.options?.maxConcurrency || recommendedConcurrency;
    console.log(`🔄 Using concurrency: ${finalConcurrency} (recommended: ${recommendedConcurrency})`);

    // 更新选项
    body.options = {
      ...body.options,
      maxConcurrency: Math.min(finalConcurrency, batchConfig.maxConcurrency)
    };

    // 验证证书数据完整性
    const invalidCertificates = body.certificates.filter((cert, index) => {
      if (!cert.recipientName || cert.recipientName.trim() === '') {
        console.warn(`❌ Certificate ${index + 1}: Missing recipient name`);
        return true;
      }
      return false;
    });

    if (invalidCertificates.length > 0) {
      return NextResponse.json(
        {
          error: `Found ${invalidCertificates.length} invalid certificate records (missing recipient name)`,
          details: 'Please check that the recipient name column is complete in your Excel/CSV file'
        },
        { status: 400 }
      );
    }

    console.log(`📊 Generating ${body.certificates.length} certificates using template: ${template.displayName}`);

    // Create batch task
    const task = BatchTaskManager.createTask(body.templateId, body.certificates);
    
    // Asynchronously process generation task
    processGenerationTask(task.id, template, body.certificates, body.options)
      .catch(error => {
        console.error(`❌ Batch generation task ${task.id} failed:`, error);
        BatchTaskManager.completeTask(task.id, false);
      });

    // Immediately return task ID
    return NextResponse.json({
      success: true,
      taskId: task.id,
      message: 'Batch generation task started',
      totalCount: body.certificates.length
    });

  } catch (error) {
    console.error('❌ Batch generation API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 处理生成任务（异步）
 */
async function processGenerationTask(
  taskId: string,
  template: any,
  certificates: any[],
  options: any = {}
) {
  try {
    console.log(`🔄 Processing generation task: ${taskId}`);

    // 动态计算最优并发数
    const optimalConcurrency = Math.min(
      options?.maxConcurrency || 8, // 提高默认并发数
      Math.max(2, Math.ceil(certificates.length / 10)), // 根据证书数量动态调整
      8 // 最大并发数限制
    );

    console.log(`📊 Using concurrency: ${optimalConcurrency} for ${certificates.length} certificates`);

    // 创建PDF生成器
    const generator = new BatchPDFGenerator(taskId, template, certificates, {
      maxConcurrency: optimalConcurrency
    });

    // 生成PDF文件
    const result = await generator.generate();

    if (result.success && result.generatedFiles.length > 0) {
      // 打包为ZIP文件
      const outputDir = generator.getOutputDirectory();
      const zipFilename = ZipPackager.generateZipFilename(`certificates_${taskId}`);
      const zipPath = path.join(outputDir, zipFilename);

      console.log(`📦 Creating ZIP package: ${zipPath}`);

      const zipResult = await ZipPackager.packDirectory(outputDir, zipPath, {
        fileFilter: (filename) => filename.endsWith('.pdf')
      });

      if (zipResult.success && zipResult.zipPath) {
        // 设置下载链接
        const downloadUrl = `/api/batch/download/${taskId}`;
        BatchTaskManager.setDownloadUrl(taskId, downloadUrl);
        
        console.log(`✅ Batch generation completed successfully: ${taskId}`);
        console.log(`📊 Generated ${result.generatedFiles.length} PDFs, ${result.errors.length} errors`);
      } else {
        throw new Error(`ZIP packaging failed: ${zipResult.error}`);
      }
    } else {
      throw new Error('No PDF files were successfully generated');
    }

  } catch (error) {
    console.error(`❌ Generation task ${taskId} failed:`, error);
    BatchTaskManager.completeTask(taskId, false);
    throw error;
  }
}

/**
 * 获取Batch Generation配置
 * GET /api/batch/generate
 */
export async function GET() {
  try {
    const batchConfig = getBatchLimitsConfig();

    return NextResponse.json({
      config: {
        maxCertificates: batchConfig.maxCertificatesPerBatch,
        maxConcurrency: batchConfig.maxConcurrency,
        supportedTemplates: TemplateManager.getBatchAvailableTemplates().map(t => ({
          id: t.id,
          name: t.displayName,
          category: t.category,
          status: t.status
        })),
        defaultOptions: {
          parallel: true,
          maxConcurrency: batchConfig.maxConcurrency,
          outputFormat: 'zip'
        }
      },
      stats: BatchTaskManager.getTaskStats()
    });

  } catch (error) {
    console.error('❌ Get batch config error:', error);
    
    return NextResponse.json(
      { error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}
