import { NextRequest, NextResponse } from 'next/server';
import { BatchTaskManager } from '@/lib/batch-task-manager';
import { BatchTaskStatus } from '@/types/certificate';
import { ZipPackager } from '@/lib/zip-packager';
import fs from 'fs';
import path from 'path';

interface RouteParams {
  params: {
    taskId: string;
  };
}

/**
 * 下载批量生成的证书文件
 * GET /api/batch/download/[taskId]
 */
export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    console.log(`📥 Download request for task: ${taskId}`);

    // 获取任务信息
    const taskProgress = BatchTaskManager.getTaskProgress(taskId);

    if (!taskProgress) {
      return NextResponse.json(
        { error: 'Task not found or expired' },
        { status: 404 }
      );
    }

    if (taskProgress.status !== BatchTaskStatus.COMPLETED) {
      return NextResponse.json(
        { error: 'Task not completed yet, cannot download' },
        { status: 400 }
      );
    }

    // 查找ZIP文件
    const outputDir = path.join(process.cwd(), 'temp', 'batch-pdfs', taskId);
    const zipFiles = fs.readdirSync(outputDir).filter(file => file.endsWith('.zip'));

    if (zipFiles.length === 0) {
      return NextResponse.json(
        { error: 'Download file not found' },
        { status: 404 }
      );
    }

    const zipFilePath = path.join(outputDir, zipFiles[0]);

    // 验证ZIP文件
    const validation = ZipPackager.validateZipFile(zipFilePath);
    if (!validation.valid) {
      return NextResponse.json(
        { error: `File validation failed: ${validation.error}` },
        { status: 500 }
      );
    }

    console.log(`📦 Serving ZIP file: ${zipFilePath} (${ZipPackager.formatFileSize(validation.size!)})`);

    // 读取文件
    const fileBuffer = fs.readFileSync(zipFilePath);

    // 标记任务为已下载
    BatchTaskManager.markTaskAsDownloaded(taskId);

    // 生成下载文件名
    const downloadFilename = `certificates_${taskId}_${new Date().toISOString().split('T')[0]}.zip`;

    // 返回文件流
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${downloadFilename}"`,
        'Content-Length': validation.size!.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error(`❌ Download error for task ${params.taskId}:`, error);
    
    return NextResponse.json(
      {
        error: 'Download failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取下载信息
 * HEAD /api/batch/download/[taskId]
 */
export async function HEAD(_request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return new NextResponse(null, { status: 400 });
    }

    // 获取任务信息
    const taskProgress = BatchTaskManager.getTaskProgress(taskId);

    if (!taskProgress) {
      return new NextResponse(null, { status: 404 });
    }

    if (taskProgress.status !== BatchTaskStatus.COMPLETED) {
      return new NextResponse(null, { status: 400 });
    }

    // 查找ZIP文件
    const outputDir = path.join(process.cwd(), 'temp', 'batch-pdfs', taskId);
    
    if (!fs.existsSync(outputDir)) {
      return new NextResponse(null, { status: 404 });
    }

    const zipFiles = fs.readdirSync(outputDir).filter(file => file.endsWith('.zip'));

    if (zipFiles.length === 0) {
      return new NextResponse(null, { status: 404 });
    }

    const zipFilePath = path.join(outputDir, zipFiles[0]);
    const validation = ZipPackager.validateZipFile(zipFilePath);

    if (!validation.valid) {
      return new NextResponse(null, { status: 500 });
    }

    // 返回文件信息头
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Length': validation.size!.toString(),
        'X-File-Count': taskProgress.completedCount.toString(),
        'X-Task-Status': taskProgress.status,
        'X-Created-At': new Date().toISOString()
      }
    });

  } catch (error) {
    console.error(`❌ Download HEAD error for task ${params.taskId}:`, error);
    return new NextResponse(null, { status: 500 });
  }
}
