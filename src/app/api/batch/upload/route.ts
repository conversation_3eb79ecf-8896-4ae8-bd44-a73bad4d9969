import { NextRequest, NextResponse } from 'next/server';
import { ServerFileParser } from '@/lib/server-file-parser';
import { FileUploadResult } from '@/types/certificate';

/**
 * 批量文件上传和解析API
 * POST /api/batch/upload
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📤 Batch file upload request received');

    // 获取表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    console.log(`📁 Processing file: ${file.name} (${file.size} bytes)`);

    // 解析文件选项
    const hasHeader = formData.get('hasHeader') === 'true';
    const skipEmptyRows = formData.get('skipEmptyRows') !== 'false';
    const maxRows = parseInt(formData.get('maxRows') as string) || 50;

    // 列映射配置
    const columnMapping = {
      recipientName: formData.get('recipientNameColumn') as string || 'A',
      date: formData.get('dateColumn') as string || 'B',
      signature: formData.get('signatureColumn') as string || 'C',
      details: formData.get('detailsColumn') as string || 'D'
    };

    // 解析文件
    const result: FileUploadResult = await ServerFileParser.parseFile(file, {
      hasHeader,
      skipEmptyRows,
      maxRows,
      columnMapping
    });

    if (!result.success) {
      console.error('❌ File parsing failed:', result.errors);

      // 提取主要错误信息
      const mainError = result.errors && result.errors.length > 0 ? result.errors[0].message : 'File parsing failed';

      return NextResponse.json(
        {
          error: mainError,
          details: result.errors,
          type: 'FILE_PARSING_ERROR'
        },
        { status: 400 }
      );
    }

    console.log(`✅ File parsed successfully: ${result.validRows}/${result.totalRows} valid rows`);

    // 返回解析结果
    return NextResponse.json({
      success: true,
      data: result.data,
      totalRows: result.totalRows,
      validRows: result.validRows,
      errors: result.errors || [],
      summary: {
        fileName: file.name,
        fileSize: file.size,
        totalRows: result.totalRows,
        validRows: result.validRows,
        errorCount: result.errors?.length || 0
      }
    });

  } catch (error) {
    console.error('❌ Batch upload API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取上传配置信息
 * GET /api/batch/upload
 */
export async function GET() {
  try {
    return NextResponse.json({
      config: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxRows: 50,
        supportedFormats: ['.xlsx', '.xls', '.csv'],
        defaultColumnMapping: {
          recipientName: 'A',
          date: 'B', 
          signature: 'C',
          details: 'D'
        },
        sampleData: [
          {
            recipientName: 'John Smith',
            date: '2024-01-15',
            signature: 'Director Lee',
            details: 'Completed Advanced Project Management Course'
          },
          {
            recipientName: 'Jane Doe',
            date: '2024-01-16',
            signature: 'Manager Wang',
            details: 'Excellent performance in team collaboration'
          }
        ]
      }
    });

  } catch (error) {
    console.error('❌ Get upload config error:', error);
    
    return NextResponse.json(
      { error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}
