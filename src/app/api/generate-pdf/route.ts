import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs/promises';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { PDFTextUtils } from '@/lib/pdf-text-utils';

/**
 * 服务器端PDF生成API
 * 解决Dancing Script字体在客户端的显示问题
 */

interface GeneratePDFRequest {
  templateId: string;
  data: CertificateData;
}

// 服务器端字体文件路径
const FONT_PATHS = {
  'Dancing Script': {
    400: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Regular.ttf'),
    500: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Medium.ttf'),
    600: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-SemiBold.ttf'),
    700: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Bold.ttf')
  },
  'Open Sans': {
    400: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-400.ttf'),
    700: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-700.ttf')
  },
  'Roboto': {
    400: path.join(process.cwd(), 'public/fonts/roboto/roboto-400.ttf'),
    700: path.join(process.cwd(), 'public/fonts/roboto/roboto-700.ttf')
  }
};

// 全局字体数据缓存
const globalFontCache = new Map<string, ArrayBuffer>();

/**
 * 预加载常用字体到内存
 */
async function preloadFonts() {
  const startTime = Date.now();
  const loadPromises: Promise<void>[] = [];

  for (const [family, weights] of Object.entries(FONT_PATHS)) {
    for (const [weight, fontPath] of Object.entries(weights)) {
      const cacheKey = `${family}-${weight}`;

      if (!globalFontCache.has(cacheKey)) {
        loadPromises.push(
          fs.readFile(fontPath)
            .then(fontBytes => {
              globalFontCache.set(cacheKey, fontBytes);
              console.log(`📁 Preloaded font: ${family} ${weight} (${Math.round(fontBytes.length / 1024)}KB)`);
            })
            .catch(error => {
              console.warn(`⚠️ Failed to preload font: ${family} ${weight}`, error);
            })
        );
      }
    }
  }

  await Promise.allSettled(loadPromises);
  const loadTime = Date.now() - startTime;
  console.log(`✅ Font preloading completed in ${loadTime}ms, cached ${globalFontCache.size} fonts`);
}

// 应用启动时预加载字体
preloadFonts();

/**
 * 服务器端字体嵌入器 - 优化版本
 */
class ServerFontEmbedder {
  private pdfDoc: PDFDocument;
  private fontCache = new Map<string, any>();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
  }

  async embedFont(family: string, weight: number = 400) {
    const cacheKey = `${family}-${weight}`;

    // 检查PDF文档级别的缓存
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey);
    }

    try {
      const startTime = Date.now();

      // 从全局缓存获取字体数据
      const globalCacheKey = `${family}-${weight}`;
      const fontBytes = globalFontCache.get(globalCacheKey);

      if (fontBytes) {
        // 使用缓存的字体数据
        const font = await this.pdfDoc.embedFont(fontBytes, {
          subset: true,
          customName: `${family.replace(/\s+/g, '')}-${weight}`
        });

        this.fontCache.set(cacheKey, font);
        const embedTime = Date.now() - startTime;
        console.log(`✅ Font embedded from cache: ${family} ${weight} (${embedTime}ms)`);
        return font;
      }

      // 如果全局缓存中没有，尝试实时加载
      const fontPath = (FONT_PATHS as any)[family]?.[weight];
      if (fontPath) {
        console.log(`📁 Loading font from disk: ${fontPath}`);

        const diskFontBytes = await fs.readFile(fontPath);
        const font = await this.pdfDoc.embedFont(diskFontBytes, {
          subset: true,
          customName: `${family.replace(/\s+/g, '')}-${weight}`
        });

        // 同时更新全局缓存
        globalFontCache.set(globalCacheKey, diskFontBytes);
        this.fontCache.set(cacheKey, font);

        const embedTime = Date.now() - startTime;
        console.log(`✅ Font loaded and embedded: ${family} ${weight} (${embedTime}ms)`);
        return font;
      }

      // 后备到标准字体
      console.log(`⚠️ No font file for ${family} ${weight}, using fallback`);
      const fallbackFont = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);
      const fallbackFont = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;
    }
  }
}

/**
 * 服务器端PDF生成器
 */
class ServerPDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc!: PDFDocument;
  private fontEmbedder!: ServerFontEmbedder;

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  async generate(): Promise<Uint8Array> {
    const startTime = Date.now();
    console.log('🚀 Starting server-side PDF generation...');

    // 创建PDF文档
    const docStartTime = Date.now();
    this.pdfDoc = await PDFDocument.create();
    this.pdfDoc.registerFontkit(fontkit);
    console.log(`📄 PDF document created in ${Date.now() - docStartTime}ms`);

    // 初始化字体嵌入器
    this.fontEmbedder = new ServerFontEmbedder(this.pdfDoc);
    
    // 创建页面
    const [width, height] = this.getPageSize();
    const page = this.pdfDoc.addPage([width, height]);
    
    // 绘制背景
    await this.drawBackground(page, width, height);
    
    // 绘制边框
    if (this.template.style.border && this.template.style.colors?.border) {
      const borderColor = this.hexToRgb(this.template.style.colors.border);
      const borderWidth = 3;

      page.drawRectangle({
        x: borderWidth / 2,
        y: borderWidth / 2,
        width: width - borderWidth,
        height: height - borderWidth,
        borderColor: rgb(borderColor.r / 255, borderColor.g / 255, borderColor.b / 255),
        borderWidth: borderWidth
      });
    }
    
    // 绘制文本元素
    const textStartTime = Date.now();
    await this.drawText(page, 'name', this.data.recipientName, width, height);
    await this.drawText(page, 'details', this.data.details, width, height);
    await this.drawText(page, 'date', this.data.date, width, height);
    await this.drawText(page, 'signature', this.data.signature, width, height);
    console.log(`📝 Text rendering completed in ${Date.now() - textStartTime}ms`);

    // 保存PDF
    const saveStartTime = Date.now();
    const pdfBytes = await this.pdfDoc.save();
    const saveTime = Date.now() - saveStartTime;
    const totalTime = Date.now() - startTime;

    console.log(`💾 PDF saved in ${saveTime}ms`);
    console.log(`✅ Server-side PDF generation completed in ${totalTime}ms (${Math.round(pdfBytes.length / 1024)}KB)`);

    return pdfBytes;
  }

  private async drawText(page: any, type: 'name' | 'details' | 'date' | 'signature', text: string, width: number, height: number) {
    const layout = this.template.layout[type];
    if (!text || !layout) return;

    // 使用layout中的字体配置，确保与批量生成一致
    const fontFamily = layout.fontFamily;
    const fontWeight = layout.fontWeight || 400;
    const fontSize = layout.fontSize;
    const color = layout.color;

    console.log(`🎨 Drawing ${type} with layout config: font=${fontFamily} ${fontWeight}, size=${fontSize}, color=${color}`);

    // 获取字体
    const font = await this.fontEmbedder.embedFont(fontFamily, fontWeight);

    // 计算位置
    const x = layout.x;
    const y = height - layout.y;

    // 特殊处理details字段，使用智能文本换行
    if (type === 'details') {
      await this.drawDetailsText(page, text, font, {
        family: fontFamily,
        size: fontSize,
        color: color,
        weight: fontWeight
      }, layout, width, height);
      return;
    }

    // 计算对齐位置
    let finalX = x;
    if (layout.align === 'center') {
      const textWidth = font.widthOfTextAtSize(text, fontSize);
      finalX = x - textWidth / 2;
    } else if (layout.align === 'right') {
      const textWidth = font.widthOfTextAtSize(text, fontSize);
      finalX = x - textWidth;
    }

    // 其他字段直接绘制
    page.drawText(text, {
      x: finalX,
      y,
      size: fontSize,
      font,
      color: (() => {
        const colorObj = this.hexToRgb(color);
        return rgb(colorObj.r, colorObj.g, colorObj.b);
      })()
    });

    console.log(`✅ Drew ${type}: "${text}" at (${finalX}, ${y}) with font ${fontFamily} ${fontWeight}`);
  }

  private async drawDetailsText(page: any, text: string, font: any, fontConfig: any, layout: any, pageWidth: number, pageHeight: number) {
    const fontSize = fontConfig.size;
    const maxWidth = layout.width;
    const startX = layout.x;
    const startY = pageHeight - layout.y;

    console.log(`🔍 Drawing details text: "${text}"`);
    console.log(`📐 Layout: x=${layout.x}px, y=${layout.y}px, width=${layout.width}px, height=${layout.height}px`);
    console.log(`📏 Calculated: startX=${startX}, startY=${startY}, maxWidth=${maxWidth}`);

    // 使用layout中的height配置
    const maxHeight = layout.height || 100;

    // 使用智能文本换行
    const lines = PDFTextUtils.wrapText(text, font, fontSize, maxWidth, maxHeight);
    const lineHeight = fontSize * 1.5;
    const bottomMargin = 50; // 定义底部边距

    // 绘制每一行
    lines.forEach((line, index) => {
      if (!line.trim()) return;

      const currentY = startY - (index * lineHeight);

      // 确保不会绘制到页面边界之外
      if (currentY < bottomMargin) {
        console.warn(`Skipping line ${index + 1} as it would exceed page boundaries`);
        return;
      }

      // 计算对齐位置
      let x = startX;
      if (layout.align === 'center') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX - lineWidth / 2;
      } else if (layout.align === 'right') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX - lineWidth;
      }

      // 确保文本不会超出页面边界
      const textWidth = font.widthOfTextAtSize(line, fontSize);
      const margin = 20;
      const clampedPosition = PDFTextUtils.clampToPageBounds(
        x, currentY, textWidth, fontSize, pageWidth, pageHeight, margin
      );

      page.drawText(line, {
        x: clampedPosition.x,
        y: clampedPosition.y,
        size: fontSize,
        font,
        color: (() => {
          const color = this.hexToRgb(fontConfig.color);
          return rgb(color.r, color.g, color.b);
        })()
      });
    });

    console.log(`✅ Drew details text: ${lines.length} lines, font: ${fontConfig.family}`);
  }

  private async drawBackground(page: any, width: number, height: number) {
    // 先绘制颜色背景
    if (this.template.style.background) {
      const bgColor = this.hexToRgb(this.template.style.background);
      page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: rgb(bgColor.r, bgColor.g, bgColor.b)
      });
    }

    // 然后绘制背景图片（如果有的话）
    if (this.template.backgroundImage) {
      try {
        await this.drawBackgroundImage(page, width, height);
      } catch (error) {
        console.warn('Failed to load background image, using color background only:', error);
      }
    }
  }

  private async drawBackgroundImage(page: any, width: number, height: number) {
    if (!this.template.backgroundImage) return;

    try {
      const imagePath = this.template.backgroundImage;

      // 如果是相对路径，转换为文件系统路径
      if (imagePath.startsWith('/')) {
        const publicPath = path.join(process.cwd(), 'public', imagePath);
        console.log(`Loading background image from: ${publicPath}`);

        const imageBytes = await fs.readFile(publicPath);

        // 根据文件扩展名确定图片类型
        let image;
        if (imagePath.toLowerCase().endsWith('.png')) {
          image = await this.pdfDoc.embedPng(imageBytes);
        } else if (imagePath.toLowerCase().endsWith('.jpg') || imagePath.toLowerCase().endsWith('.jpeg')) {
          image = await this.pdfDoc.embedJpg(imageBytes);
        } else {
          throw new Error(`Unsupported image format: ${imagePath}`);
        }

        // 获取图片的原始尺寸
        const imageDims = image.scale(1);

        // 计算缩放比例以适应页面
        const scaleX = width / imageDims.width;
        const scaleY = height / imageDims.height;
        const scale = Math.min(scaleX, scaleY);

        // 计算居中位置
        const scaledWidth = imageDims.width * scale;
        const scaledHeight = imageDims.height * scale;
        const x = (width - scaledWidth) / 2;
        const y = (height - scaledHeight) / 2;

        // 绘制图片，保持宽高比并居中
        page.drawImage(image, {
          x,
          y,
          width: scaledWidth,
          height: scaledHeight,
        });

        console.log(`✅ Background image drawn successfully: ${imagePath}`);
      } else {
        throw new Error('Only relative paths starting with "/" are supported');
      }
    } catch (error) {
      console.error(`❌ Error drawing background image:`, error);
      throw error;
    }
  }

  private getPageSize(): [number, number] {
    return this.template.orientation === 'landscape' ? [842, 595] : [595, 842];
  }

  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return { r: 0, g: 0, b: 0 };
    }
    return {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: GeneratePDFRequest = await request.json();
    
    console.log('📥 Received PDF generation request:', {
      templateId: body.templateId,
      recipientName: body.data.recipientName
    });
    
    // 获取模板
    const template = TemplateManager.getTemplateById(body.templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // 生成PDF
    const generator = new ServerPDFGenerator(template, body.data);
    const pdfBytes = await generator.generate();
    
    console.log(`✅ PDF generated successfully: ${pdfBytes.length} bytes`);
    
    // 返回PDF
    return new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="CertificateMaker.App-${body.data.recipientName}.pdf"`,
        'Content-Length': pdfBytes.length.toString()
      }
    });
    
  } catch (error) {
    console.error('❌ Server PDF generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'PDF generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
