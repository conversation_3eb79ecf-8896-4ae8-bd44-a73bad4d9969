'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileParser } from '@/lib/file-parser';
import { BatchPerformanceMonitor } from '@/lib/batch-performance-monitor';
import { Download, TestTube, FileText, CheckCircle, AlertCircle } from 'lucide-react';

export default function BatchTestPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // 生成测试数据
  const generateTestData = (count: number) => {
    const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
    const signatures = ['李主任', '王经理', '张总监', '刘部长', '陈校长'];
    const details = [
      '完成了高级项目管理课程',
      '在团队协作中表现优秀',
      '获得年度最佳员工奖',
      '完成了专业技能培训',
      '参与了重要项目开发'
    ];

    const data = [];
    for (let i = 0; i < count; i++) {
      data.push({
        recipientName: names[i % names.length] + (i > names.length ? i : ''),
        date: new Date(2024, 0, 1 + i).toISOString().split('T')[0],
        signature: signatures[i % signatures.length],
        details: details[i % details.length],
        rowIndex: i + 1
      });
    }
    return data;
  };

  // 生成测试CSV内容
  const generateTestCSV = (count: number) => {
    const data = generateTestData(count);
    const header = '收件人姓名,日期,签名,证书详情\n';
    const rows = data.map(item => 
      `${item.recipientName},${item.date},${item.signature},${item.details}`
    ).join('\n');
    return header + rows;
  };

  // 下载测试文件
  const downloadTestFile = (count: number, format: 'csv' | 'xlsx') => {
    if (format === 'csv') {
      const csvContent = generateTestCSV(count);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `test_certificates_${count}.csv`;
      link.click();
    }
  };

  // 测试文件解析性能
  const testFileParsingPerformance = async (count: number) => {
    const startTime = Date.now();
    
    try {
      const csvContent = generateTestCSV(count);
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const file = new File([blob], `test_${count}.csv`, { type: 'text/csv' });
      
      const result = await FileParser.parseFile(file);
      const endTime = Date.now();
      
      return {
        success: result.success,
        count,
        parseTime: endTime - startTime,
        validRows: result.validRows,
        errors: result.errors?.length || 0,
        memoryUsage: (window.performance as any).memory?.usedJSHeapSize || 0
      };
    } catch (error) {
      return {
        success: false,
        count,
        parseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : '解析失败'
      };
    }
  };

  // 运行性能测试
  const runPerformanceTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const testCases = [10, 50, 100, 500, 1000];
    const results = [];
    
    for (const count of testCases) {
      console.log(`🧪 Testing file parsing with ${count} records...`);
      
      const result = await testFileParsingPerformance(count);
      results.push(result);
      setTestResults([...results]);
      
      // 添加延迟避免浏览器卡顿
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    setIsRunning(false);
    console.log('🎉 Performance tests completed');
  };

  // 测试API端点
  const testAPIEndpoints = async () => {
    const endpoints = [
      { name: '上传配置', url: '/api/batch/upload', method: 'GET' },
      { name: '生成配置', url: '/api/batch/generate', method: 'GET' }
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.url, { method: endpoint.method });
        const data = await response.json();
        console.log(`✅ ${endpoint.name} API test passed:`, data);
      } catch (error) {
        console.error(`❌ ${endpoint.name} API test failed:`, error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            批量功能测试页面
          </h1>
          <p className="text-gray-600">
            测试批量生成功能的性能和稳定性
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 测试工具 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              <TestTube className="h-5 w-5 inline mr-2" />
              测试工具
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">下载测试文件</h3>
                <div className="flex flex-wrap gap-2">
                  {[10, 50, 100, 500, 1000].map(count => (
                    <Button
                      key={count}
                      variant="outline"
                      size="sm"
                      onClick={() => downloadTestFile(count, 'csv')}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {count} 条记录
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900 mb-2">性能测试</h3>
                <Button
                  onClick={runPerformanceTests}
                  disabled={isRunning}
                  className="w-full"
                >
                  {isRunning ? '测试中...' : '运行解析性能测试'}
                </Button>
              </div>

              <div>
                <h3 className="font-medium text-gray-900 mb-2">API测试</h3>
                <Button
                  onClick={testAPIEndpoints}
                  variant="outline"
                  className="w-full"
                >
                  测试API端点
                </Button>
              </div>
            </div>
          </Card>

          {/* 测试结果 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              <FileText className="h-5 w-5 inline mr-2" />
              测试结果
            </h2>
            
            {testResults.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                暂无测试结果，请运行测试
              </p>
            ) : (
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      result.success 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {result.success ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        )}
                        <span className="font-medium">
                          {result.count} 条记录
                        </span>
                      </div>
                      <Badge variant={result.success ? "default" : "destructive"}>
                        {result.parseTime}ms
                      </Badge>
                    </div>
                    
                    {result.success ? (
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>有效行数: {result.validRows}</p>
                        <p>错误数量: {result.errors}</p>
                        <p>内存使用: {Math.round(result.memoryUsage / 1024 / 1024)}MB</p>
                        <p>处理速度: {Math.round(result.count / (result.parseTime / 1000))} 条/秒</p>
                      </div>
                    ) : (
                      <p className="text-sm text-red-600">{result.error}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>

        {/* 性能监控概览 */}
        <Card className="p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            性能监控概览
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">浏览器内存</h3>
              <p className="text-2xl font-bold text-blue-600">
                {typeof window !== 'undefined' && (window.performance as any).memory
                  ? Math.round((window.performance as any).memory.usedJSHeapSize / 1024 / 1024)
                  : 'N/A'
                }MB
              </p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900">测试完成</h3>
              <p className="text-2xl font-bold text-green-600">
                {testResults.filter(r => r.success).length}
              </p>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-medium text-red-900">测试失败</h3>
              <p className="text-2xl font-bold text-red-600">
                {testResults.filter(r => !r.success).length}
              </p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-900">平均速度</h3>
              <p className="text-2xl font-bold text-purple-600">
                {testResults.length > 0
                  ? Math.round(
                      testResults
                        .filter(r => r.success)
                        .reduce((sum, r) => sum + (r.count / (r.parseTime / 1000)), 0) /
                      testResults.filter(r => r.success).length
                    )
                  : 0
                } 条/秒
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
