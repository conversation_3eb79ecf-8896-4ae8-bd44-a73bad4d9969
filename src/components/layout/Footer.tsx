import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface FooterProps {
  className?: string;
}

export default function Footer({ className }: FooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn(
      'border-t bg-background',
      className
    )}>
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 relative">
                <Image
                  src="/logo.png"
                  alt="Certificate Maker Logo"
                  width={32}
                  height={32}
                  className="rounded-lg object-contain"
                />
              </div>
              <span className="font-bold text-xl">Certificate Maker</span>
            </div>
            <p className="text-sm text-muted-foreground max-w-xs">
              Create beautiful, professional certificates online for free. No registration required.
            </p>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                Contact: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>

          {/* Certificate Templates */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Certificate Templates</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/certificate-templates"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  All Templates
                </Link>
              </li>
              <li>
                <Link
                  href="/certificate-templates/completion"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Completion Certificates
                </Link>
              </li>
              {/* Bulk Generator Link - Hidden */}
              {/*
              <li>
                <Link
                  href="/bulk-certificate-generator"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Bulk Generator
                </Link>
              </li>
              */}
            </ul>
          </div>

          {/* Tools */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Tools</h3>
            <ul className="space-y-2 text-sm">
              {/* Bulk Certificate Generator Link - Hidden */}
              {/*
              <li>
                <Link
                  href="/bulk-certificate-generator"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Bulk Certificate Generator
                </Link>
              </li>
              */}
              <li>
                <Link
                  href="/certificate-templates"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Template Gallery
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Support</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/privacy"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

        </div>

        {/* Bottom */}
        <div className="mt-12 pt-8 border-t flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4">
            <p className="text-sm text-muted-foreground">
              © {currentYear} CertificateMaker.App. All rights reserved.
            </p>
          </div>

          <div className="text-sm text-muted-foreground">
            Made with ❤️ for creating professional certificates
          </div>
        </div>
      </div>
    </footer>
  );
}
