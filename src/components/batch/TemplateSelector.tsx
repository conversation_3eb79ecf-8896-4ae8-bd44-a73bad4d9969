'use client';

import React, { useState } from 'react';
import { Check, ChevronDown, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CertificateTemplate, CertificateCategory } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';

interface TemplateSelectorProps {
  selectedTemplate: CertificateTemplate | null;
  onTemplateSelect: (template: CertificateTemplate) => void;
  disabled?: boolean;
  preSelectedCategory?: CertificateCategory;
}

export default function TemplateSelector({
  selectedTemplate,
  onTemplateSelect,
  disabled = false,
  preSelectedCategory
}: TemplateSelectorProps) {
  // Get templates available for batch generation and organize by category
  const batchAvailableTemplates = TemplateManager.getBatchAvailableTemplates();
  const templatesByCategory = batchAvailableTemplates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<CertificateCategory, CertificateTemplate[]>);

  // 获取可用分类列表
  const availableCategories = Object.keys(templatesByCategory) as CertificateCategory[];
  const hasOnlyOneCategory = availableCategories.length === 1;

  // 如果只有一个分类，自动选择它；否则使用预选分类或null
  const [selectedCategory, setSelectedCategory] = useState<CertificateCategory | null>(
    hasOnlyOneCategory ? availableCategories[0] : (preSelectedCategory || null)
  );
  const [isExpanded, setIsExpanded] = useState(false);

  // 获取分类显示名称
  const getCategoryDisplayName = (category: CertificateCategory): string => {
    const names = {
      [CertificateCategory.ACHIEVEMENT]: 'Achievement Certificates',
      [CertificateCategory.COMPLETION]: 'Completion Certificates',
      [CertificateCategory.PARTICIPATION]: 'Participation Certificates',
      [CertificateCategory.EXCELLENCE]: 'Excellence Certificates',
      [CertificateCategory.CUSTOM]: 'Custom Certificates'
    };
    return names[category] || category;
  };

  // 获取分类描述
  const getCategoryDescription = (category: CertificateCategory): string => {
    const descriptions = {
      [CertificateCategory.ACHIEVEMENT]: 'For recognizing specific achievements and milestones',
      [CertificateCategory.COMPLETION]: 'For confirming course or project completion',
      [CertificateCategory.PARTICIPATION]: 'For confirming event or activity participation',
      [CertificateCategory.EXCELLENCE]: 'For recognizing outstanding performance and contributions',
      [CertificateCategory.CUSTOM]: 'Custom purpose certificate templates'
    };
    return descriptions[category] || '';
  };

  return (
    <Card className="p-8 shadow-lg border-0 bg-white">
      <div className="space-y-8">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Select Certificate Template
          </h3>
          <p className="text-sm text-gray-600">
            Choose the certificate template that best fits your needs
          </p>
        </div>

        {/* 当前选择显示 */}
        {selectedTemplate && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm ${
                  selectedTemplate.orientation === 'landscape' ? 'w-32 h-24' : 'w-24 h-32'
                }`}>
                  <img
                    src={selectedTemplate.preview}
                    alt={selectedTemplate.displayName}
                    className="w-full h-full object-contain p-1"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-blue-900 mb-1">{selectedTemplate.displayName}</h4>
                  <p className="text-sm text-blue-700 mb-2">{selectedTemplate.description}</p>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {getCategoryDisplayName(selectedTemplate.category)}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {selectedTemplate.orientation === 'landscape' ? 'Landscape' : 'Portrait'}
                    </Badge>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                disabled={disabled}
              >
                {isExpanded ? 'Collapse' : 'Change Template'}
                <ChevronDown className={`h-4 w-4 ml-1 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>
        )}

        {/* 模板选择区域 */}
        {(!selectedTemplate || isExpanded) && (
          <div className="space-y-4">
            {/* Category Selection - 只在有多个分类时显示 */}
            {!hasOnlyOneCategory && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.keys(templatesByCategory).map((category) => {
                  const categoryEnum = category as CertificateCategory;
                  const isSelected = selectedCategory === categoryEnum;
                  const templateCount = templatesByCategory[categoryEnum].length;

                  return (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(isSelected ? null : categoryEnum)}
                      disabled={disabled}
                      className={`
                        p-4 rounded-lg border-2 text-left transition-all duration-200 hover:shadow-md
                        ${isSelected
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 bg-white'
                        }
                        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                      `}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <h4 className={`font-medium text-sm ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                          {getCategoryDisplayName(categoryEnum)}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          {templateCount}
                        </Badge>
                      </div>
                      <p className={`text-xs ${isSelected ? 'text-blue-700' : 'text-gray-600'}`}>
                        {getCategoryDescription(categoryEnum)}
                      </p>
                    </button>
                  );
                })}
              </div>
            )}

            {/* Template List */}
            {selectedCategory && (
              <div className="space-y-4">
                {/* 只在有多个分类时显示分类标题 */}
                {!hasOnlyOneCategory && (
                  <h4 className="text-lg font-semibold text-gray-900">
                    {getCategoryDisplayName(selectedCategory)}
                  </h4>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {templatesByCategory[selectedCategory].map((template) => {
                    const isSelected = selectedTemplate?.id === template.id;

                    return (
                      <button
                        key={template.id}
                        onClick={() => {
                          onTemplateSelect(template);
                          setIsExpanded(false);
                        }}
                        disabled={disabled}
                        className={`
                          p-6 rounded-lg border-2 text-left transition-all duration-200 group
                          ${isSelected
                            ? 'border-blue-500 bg-blue-50 shadow-lg'
                            : 'border-gray-200 hover:border-blue-300 bg-white hover:shadow-lg'
                          }
                          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                        `}
                      >
                        <div className="space-y-4">
                          {/* Large Template Preview */}
                          <div className="relative">
                            <div className={`w-full bg-gray-100 border border-gray-200 rounded-lg overflow-hidden shadow-sm group-hover:shadow-md transition-shadow ${
                              template.orientation === 'landscape' ? 'h-24' : 'h-32'
                            }`}>
                              <img
                                src={template.preview}
                                alt={template.displayName}
                                className="w-full h-full object-contain p-2"
                              />
                            </div>
                            {isSelected && (
                              <div className="absolute top-2 right-2 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                <Check className="h-4 w-4 text-white" />
                              </div>
                            )}
                          </div>

                          {/* Template Info */}
                          <div className="space-y-2">
                            <h5 className={`text-lg font-semibold ${isSelected ? 'text-blue-900' : 'text-gray-900 group-hover:text-blue-900'} transition-colors`}>
                              {template.displayName}
                            </h5>
                            <p className={`text-sm ${isSelected ? 'text-blue-700' : 'text-gray-600'} line-clamp-2`}>
                              {template.description}
                            </p>
                            <div className="flex items-center space-x-2 pt-1">
                              <Badge variant="outline" className="text-xs">
                                {template.orientation === 'landscape' ? 'Landscape' : 'Portrait'}
                              </Badge>
                              {template.tags.slice(0, 2).map((tag) => (
                                <Badge key={tag} variant="secondary" className="text-xs capitalize">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Help Text */}
        {!selectedTemplate && (
          <div className="text-center py-12 text-gray-500">
            <div className="max-w-md mx-auto">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">Select a Template</p>
              <p className="text-sm">Choose a certificate category above, then select a specific template to get started</p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
