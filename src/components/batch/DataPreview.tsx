'use client';

import React, { useState } from 'react';
import { Eye, EyeOff, AlertTriangle, CheckCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BatchCertificateData, FileParseError } from '@/types/certificate';

interface DataPreviewProps {
  data: BatchCertificateData[];
  errors?: FileParseError[];
  totalRows: number;
  validRows: number;
  fileName: string;
  onConfirm: () => void;
  onEdit: () => void;
  onChangeTemplate?: () => void;
  isLoading?: boolean;
}

export default function DataPreview({
  data,
  errors = [],
  totalRows,
  validRows,
  fileName,
  onConfirm,
  onEdit,
  onChangeTemplate,
  isLoading = false
}: DataPreviewProps) {
  const [showErrors, setShowErrors] = useState(false);
  const [previewLimit, setPreviewLimit] = useState(10);

  const hasErrors = errors.length > 0;
  const canProceed = validRows > 0;

  return (
    <Card className="p-8 shadow-lg border-0 bg-white">
      <div className="space-y-8">
        {/* 文件信息头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Data Preview</h3>
              <p className="text-sm text-gray-600">{fileName}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant={hasErrors ? "destructive" : "default"}>
              {validRows}/{totalRows} Valid
            </Badge>
            {hasErrors && (
              <Badge variant="outline">
                {errors.length} Errors
              </Badge>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">Valid Records</p>
                <p className="text-2xl font-bold text-blue-600">{validRows}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Total Rows</p>
                <p className="text-2xl font-bold text-gray-600">{totalRows}</p>
              </div>
            </div>
          </div>

          {hasErrors && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-red-900">Error Count</p>
                  <p className="text-2xl font-bold text-red-600">{errors.length}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 错误信息 */}
        {hasErrors && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-red-900">Data Errors</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowErrors(!showErrors)}
                className="text-red-600 hover:text-red-700"
              >
                {showErrors ? (
                  <>
                    <EyeOff className="h-4 w-4 mr-1" />
                    Hide Errors
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-1" />
                    View Errors
                  </>
                )}
              </Button>
            </div>

            {showErrors && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                <div className="space-y-2">
                  {errors.slice(0, 20).map((error, index) => (
                    <div key={index} className="text-sm">
                      <span className="font-medium text-red-900">
                        Row {error.row}
                        {error.column && ` (${error.column})`}:
                      </span>
                      <span className="text-red-700 ml-2">{error.message}</span>
                      {error.value && (
                        <span className="text-red-600 ml-2 font-mono text-xs">
                          &quot;{error.value}&quot;
                        </span>
                      )}
                    </div>
                  ))}
                  {errors.length > 20 && (
                    <p className="text-sm text-red-600 font-medium">
                      {errors.length - 20} more errors...
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 数据预览表格 */}
        {data.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-gray-900">Data Preview</h4>
              {data.length > previewLimit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewLimit(previewLimit === 10 ? data.length : 10)}
                >
                  {previewLimit === 10 ? 'Show All' : 'Show First 10'}
                </Button>
              )}
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Row
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recipient Name
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Signature
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Details
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data.slice(0, previewLimit).map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {item.rowIndex || index + 1}
                        </td>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">
                          {item.recipientName}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.date}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.signature}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate">
                          {item.details}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {data.length > previewLimit && previewLimit === 10 && (
              <p className="text-sm text-gray-500 text-center">
                {data.length - previewLimit} more records...
              </p>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={onEdit}
              disabled={isLoading}
            >
              Upload Different File
            </Button>
            {onChangeTemplate && (
              <Button
                variant="outline"
                onClick={onChangeTemplate}
                disabled={isLoading}
                className="text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                Change Template
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {hasErrors && (
              <p className="text-sm text-amber-600">
                ⚠️ Errors found, only valid data will be processed
              </p>
            )}
            <Button
              onClick={onConfirm}
              disabled={!canProceed || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Processing...' : `Generate ${validRows} Certificates`}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
