'use client';

import React, { useEffect, useState } from 'react';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Download, 
  X,
  Loader2,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { BatchGenerationProgress, BatchTaskStatus } from '@/types/certificate';

interface ProgressMonitorProps {
  taskId: string;
  onComplete: (downloadUrl: string) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export default function ProgressMonitor({
  taskId,
  onComplete,
  onError,
  onCancel
}: ProgressMonitorProps) {
  const [progress, setProgress] = useState<BatchGenerationProgress | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [maxRetries] = useState(30); // 最多重试30次（60秒）

  // 直接下载文件
  const handleDownload = async () => {
    try {
      const downloadUrl = `/api/batch/download/${taskId}`;
      console.log('🔗 Starting download from:', downloadUrl);

      // 直接下载文件而不是打开新页面
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }

      // 获取文件内容
      const blob = await response.blob();

      // 从响应头获取文件名，或使用默认文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'certificates.zip';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ Download completed:', filename);

    } catch (error) {
      console.error('❌ Download error:', error);
      // 如果直接下载失败，回退到原来的方式
      onComplete(`/api/batch/download/${taskId}`);
    }
  };

  // 轮询进度
  useEffect(() => {
    if (!isPolling || !taskId) return;

    const pollProgress = async () => {
      try {
        const response = await fetch(`/api/batch/progress/${taskId}`);

        if (!response.ok) {
          // 如果是404错误且重试次数未超限，继续重试
          if (response.status === 404 && retryCount < maxRetries) {
            console.log(`⏳ Task ${taskId} not found yet, retrying... (${retryCount + 1}/${maxRetries})`);
            setRetryCount(prev => prev + 1);
            return; // 继续轮询
          }

          // 其他错误或重试次数超限
          const result = await response.json();
          throw new Error(result.error || 'Failed to get progress');
        }

        const result = await response.json();
        console.log(`📊 Progress API response:`, result);

        // 确保数据结构正确
        const progressData = {
          taskId: result.taskId || taskId,
          status: result.status,
          totalCount: result.totalCount || 0,
          completedCount: result.completedCount || 0,
          failedCount: result.failedCount || 0,
          progress: result.progress || 0,
          errors: result.errors || [],
          estimatedTimeRemaining: result.estimatedTimeRemaining,
          currentItem: result.currentItem
        };

        setProgress(progressData);
        setRetryCount(0); // 重置重试计数

        // 检查是否完成
        if (progressData.status === BatchTaskStatus.COMPLETED) {
          setIsPolling(false);
          console.log(`✅ Task ${taskId} completed, triggering download`);
          onComplete(`/api/batch/download/${taskId}`);
        } else if (progressData.status === BatchTaskStatus.FAILED) {
          setIsPolling(false);
          setError('Generation failed');
          onError('Batch generation failed');
        } else if (progressData.status === BatchTaskStatus.CANCELLED) {
          setIsPolling(false);
          setError('Task cancelled');
        }

      } catch (err) {
        console.error('Progress polling error:', err);

        // 如果重试次数未超限，继续重试
        if (retryCount < maxRetries) {
          console.log(`⚠️ Polling error, retrying... (${retryCount + 1}/${maxRetries})`);
          setRetryCount(prev => prev + 1);
          return;
        }

        // 重试次数超限，报告错误
        setError(err instanceof Error ? err.message : 'Failed to get progress');
        setIsPolling(false);
        onError(err instanceof Error ? err.message : 'Failed to get progress');
      }
    };

    // 立即执行一次
    pollProgress();

    // 设置定时器
    const interval = setInterval(pollProgress, 2000); // 每2秒轮询一次

    return () => clearInterval(interval);
  }, [taskId, isPolling, onComplete, onError, retryCount, maxRetries]);

  // 取消任务
  const handleCancel = async () => {
    try {
      const response = await fetch(`/api/batch/progress/${taskId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setIsPolling(false);
        onCancel();
      } else {
        const result = await response.json();
        setError(result.error || '取消失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '取消失败');
    }
  };

  // 格式化时间
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  // 获取状态显示
  const getStatusDisplay = (status: BatchTaskStatus) => {
    switch (status) {
      case BatchTaskStatus.PENDING:
        return {
          icon: <Clock className="h-5 w-5 text-yellow-500" />,
          text: 'Pending',
          color: 'bg-yellow-100 text-yellow-800'
        };
      case BatchTaskStatus.PROCESSING:
        return {
          icon: <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />,
          text: 'Generating',
          color: 'bg-blue-100 text-blue-800'
        };
      case BatchTaskStatus.COMPLETED:
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          text: 'Completed',
          color: 'bg-green-100 text-green-800'
        };
      case BatchTaskStatus.FAILED:
        return {
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
          text: 'Failed',
          color: 'bg-red-100 text-red-800'
        };
      case BatchTaskStatus.CANCELLED:
        return {
          icon: <X className="h-5 w-5 text-gray-500" />,
          text: 'Cancelled',
          color: 'bg-gray-100 text-gray-800'
        };
      default:
        return {
          icon: <Clock className="h-5 w-5 text-gray-500" />,
          text: 'Unknown',
          color: 'bg-gray-100 text-gray-800'
        };
    }
  };

  // Show progress structure immediately, even if data isn't loaded yet
  const displayProgress = progress || {
    taskId: taskId,
    status: 'processing' as any,
    totalCount: 0,
    completedCount: 0,
    failedCount: 0,
    progress: 0,
    errors: [],
    estimatedTimeRemaining: undefined,
    currentItem: undefined
  };

  const statusDisplay = getStatusDisplay(displayProgress.status);

  return (
    <Card className="p-8 shadow-lg border-0 bg-white">
      <div className="space-y-8">
        {/* 任务状态头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Bulk Generation Progress</h3>
              <p className="text-sm text-gray-600">Task ID: {taskId}</p>
            </div>
          </div>
          
          <Badge className={statusDisplay.color}>
            <div className="flex items-center space-x-1">
              {statusDisplay.icon}
              <span>{statusDisplay.text}</span>
            </div>
          </Badge>
        </div>

        {/* 进度条 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Progress: {displayProgress.completedCount + displayProgress.failedCount} / {displayProgress.totalCount}
            </span>
            <span className="text-sm text-gray-500">
              {displayProgress.progress}%
            </span>
          </div>
          
          <Progress value={displayProgress.progress} className="h-3" />

          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Completed: {displayProgress.completedCount}</span>
            {displayProgress.failedCount > 0 && (
              <span className="text-red-600">Failed: {displayProgress.failedCount}</span>
            )}
            {displayProgress.estimatedTimeRemaining && (
              <span>Estimated remaining: {formatTime(displayProgress.estimatedTimeRemaining)}</span>
            )}
          </div>
        </div>

        {/* 当前处理项目 */}
        {displayProgress.currentItem && displayProgress.status === BatchTaskStatus.PROCESSING && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              Currently processing: {displayProgress.currentItem}
            </p>
          </div>
        )}

        {/* 错误信息 */}
        {displayProgress.errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <h4 className="font-medium text-red-900">Processing Errors</h4>
            </div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {displayProgress.errors.slice(0, 5).map((error, index) => (
                <p key={index} className="text-sm text-red-700">
                  Row {error.rowIndex}: {error.message}
                </p>
              ))}
              {displayProgress.errors.length > 5 && (
                <p className="text-sm text-red-600 font-medium">
                  {displayProgress.errors.length - 5} more errors...
                </p>
              )}
            </div>
          </div>
        )}

        {/* 系统错误 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div>
            {(displayProgress.status === BatchTaskStatus.PENDING || displayProgress.status === BatchTaskStatus.PROCESSING) && (
              <Button
                variant="outline"
                onClick={handleCancel}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-1" />
                Cancel Task
              </Button>
            )}
          </div>

          <div>
            {displayProgress.status === BatchTaskStatus.COMPLETED && (
              <Button
                onClick={handleDownload}
                className="bg-green-600 hover:bg-green-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Certificates
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
