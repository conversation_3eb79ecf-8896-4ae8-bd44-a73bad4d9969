'use client';

import React, { useState } from 'react';
import { 
  FileText, 
  Upload, 
  Eye, 
  Download, 
  CheckCircle,
  ChevronRight,
  ChevronDown,
  Info,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BatchGuideProps {
  onStartBatch: () => void;
  className?: string;
}

export default function BatchGuide({ onStartBatch, className }: BatchGuideProps) {
  const [expandedStep, setExpandedStep] = useState<number | null>(null);

  const steps = [
    {
      id: 1,
      title: 'Select Certificate Template',
      description: 'Choose the appropriate certificate type from various professional templates',
      icon: <FileText className="h-6 w-6 text-blue-600" />,
      details: [
        'Browse four categories of certificate templates: Achievement, Completion, Participation, Excellence',
        'Each category has multiple beautiful designs to choose from',
        'Supports both landscape and portrait layouts',
        'All templates are professionally designed to ensure print quality'
      ],
      tips: 'Recommend choosing the appropriate category based on certificate purpose, such as "Completion Certificate" for course completion'
    },
    {
      id: 2,
      title: 'Prepare Data File',
      description: 'Prepare Excel or CSV file according to specified format',
      icon: <Upload className="h-6 w-6 text-green-600" />,
      details: [
        'Supports .xlsx, .xls, .csv formats',
        'File size should not exceed 10MB',
        'Recommend including header row for data identification',
        'Supports up to 1000 rows of data'
      ],
      tips: 'Ensure data integrity, blank rows will be automatically skipped',
      format: {
        columns: [
          { name: 'Column A', field: 'Recipient Name', example: '张三', required: true },
          { name: 'Column B', field: 'Date', example: '2024-01-15', required: true },
          { name: 'Column C', field: 'Signature', example: '李主任', required: true },
          { name: 'Column D', field: 'Certificate Details', example: 'Completed Advanced Project Management Course', required: true }
        ]
      }
    },
    {
      id: 3,
      title: 'Upload and Preview',
      description: 'Upload file and check parsing results',
      icon: <Eye className="h-6 w-6 text-purple-600" />,
      details: [
        'System automatically parses file content',
        'Real-time display of valid data and error information',
        'Supports data preview and error correction',
        'Start generation after confirming no errors'
      ],
      tips: 'Carefully check preview data to ensure all information is correct'
    },
    {
      id: 4,
      title: 'Batch Generation',
      description: 'System automatically generates all certificate PDF files',
      icon: <Download className="h-6 w-6 text-orange-600" />,
      details: [
        'Parallel processing to improve generation efficiency',
        'Real-time display of generation progress',
        'Automatically handle errors and exceptions',
        'Automatically package as ZIP file after generation'
      ],
      tips: 'Please keep network connection stable during generation and avoid closing the page'
    }
  ];

  const toggleStep = (stepId: number) => {
    setExpandedStep(expandedStep === stepId ? null : stepId);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 介绍部分 */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <FileText className="h-8 w-8 text-white" />
            </div>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Bulk Certificate Generation
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Generate multiple certificates at once, supports Excel and CSV file import, automatically processes data and generates high-quality PDF certificate files
            </p>
          </div>
          <div className="flex justify-center space-x-4">
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              Up to 1000 certificates
            </Badge>
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              Supports multiple formats
            </Badge>
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              Auto-package download
            </Badge>
          </div>
        </div>
      </Card>

      {/* 步骤指南 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">操作步骤</h3>
        
        {steps.map((step, index) => (
          <Card key={step.id} className="overflow-hidden">
            <div
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleStep(step.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {step.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">
                      步骤 {step.id}: {step.title}
                    </h4>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  {expandedStep === step.id ? (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>
            </div>

            {expandedStep === step.id && (
              <div className="px-4 pb-4 border-t border-gray-100">
                <div className="pt-4 space-y-4">
                  {/* 详细说明 */}
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">详细说明</h5>
                    <ul className="space-y-1">
                      {step.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start space-x-2 text-sm text-gray-600">
                          <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* 数据格式说明 */}
                  {step.format && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">数据格式要求</h5>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {step.format.columns.map((col, idx) => (
                            <div key={idx} className="flex items-center space-x-3">
                              <Badge variant="outline" className="text-xs">
                                {col.name}
                              </Badge>
                              <span className="text-sm font-medium text-gray-900">
                                {col.field}
                              </span>
                              {col.required && (
                                <span className="text-xs text-red-600">*必填</span>
                              )}
                            </div>
                          ))}
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <p className="text-xs text-gray-600">
                            示例数据：张三 | 2024-01-15 | 李主任 | Completed Advanced Project Management Course
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 提示信息 */}
                  {step.tips && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-blue-800">{step.tips}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* Important Notes */}
      <Card className="p-4 bg-yellow-50 border-yellow-200">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-yellow-900">Important Notes</h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• Please keep network connection stable during generation</li>
              <li>• Large batches are recommended to be processed in smaller batches to avoid timeout</li>
              <li>• Generated files will be automatically cleaned up after 24 hours</li>
              <li>• If you encounter problems, please check data format or contact technical support</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 开始按钮 */}
      <div className="text-center pt-4">
        <Button
          onClick={onStartBatch}
          size="lg"
          className="bg-blue-600 hover:bg-blue-700 px-8 py-4 text-lg"
        >
          开始Batch Generation
          <ChevronRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
