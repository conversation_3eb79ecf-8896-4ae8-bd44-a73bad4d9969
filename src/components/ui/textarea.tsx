import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean
  characterCount?: {
    current: number
    max: number
  }
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, error, characterCount, ...props }, ref) => {
    return (
      <div className="relative">
        <textarea
          className={cn(
            "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            // Mobile optimization
            "md:text-sm text-base", // English comment needed
            "resize-none", // English comment needed
            error && "border-red-500 focus-visible:ring-red-500",
            className
          )}
          ref={ref}
          {...props}
        />
        {characterCount && (
          <div className={cn(
            "absolute right-2 bottom-2 text-xs pointer-events-none",
            characterCount.current > characterCount.max * 0.9 && "text-yellow-600",
            characterCount.current >= characterCount.max && "text-red-600",
            characterCount.current <= characterCount.max * 0.8 && "text-muted-foreground"
          )}>
            {characterCount.current}/{characterCount.max}
          </div>
        )}
      </div>
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
