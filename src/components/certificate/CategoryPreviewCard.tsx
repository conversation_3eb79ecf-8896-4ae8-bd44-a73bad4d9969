'use client';

import Link from 'next/link';
import Image from 'next/image';
import { TemplateCategory } from '@/types/certificate';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, Award, BookOpen, Users, Star } from 'lucide-react';

interface CategoryPreviewCardProps {
  category: TemplateCategory;
  className?: string;
}

// 获取分类图标
const getCategoryIcon = (categoryId: string) => {
  switch (categoryId) {
    case 'achievement':
      return <Award className="w-6 h-6" />;
    case 'completion':
      return <BookOpen className="w-6 h-6" />;
    case 'participation':
      return <Users className="w-6 h-6" />;
    case 'excellence':
      return <Star className="w-6 h-6" />;
    default:
      return <Award className="w-6 h-6" />;
  }
};

// 获取分类颜色主题
const getCategoryColors = (categoryId: string) => {
  switch (categoryId) {
    case 'achievement':
      return {
        bg: 'bg-blue-50',
        text: 'text-blue-600',
        border: 'border-blue-200',
        hover: 'hover:border-blue-300'
      };
    case 'completion':
      return {
        bg: 'bg-green-50',
        text: 'text-green-600',
        border: 'border-green-200',
        hover: 'hover:border-green-300'
      };
    case 'participation':
      return {
        bg: 'bg-purple-50',
        text: 'text-purple-600',
        border: 'border-purple-200',
        hover: 'hover:border-purple-300'
      };
    case 'excellence':
      return {
        bg: 'bg-yellow-50',
        text: 'text-yellow-600',
        border: 'border-yellow-200',
        hover: 'hover:border-yellow-300'
      };
    default:
      return {
        bg: 'bg-gray-50',
        text: 'text-gray-600',
        border: 'border-gray-200',
        hover: 'hover:border-gray-300'
      };
  }
};

export default function CategoryPreviewCard({ category, className = '' }: CategoryPreviewCardProps) {
  const colors = getCategoryColors(category.id);

  // 检查分类是否被禁用
  const isDisabled = category.enabled === false;

  const cardContent = (
    <Card className={`h-full transition-all duration-300 ${
      isDisabled
        ? 'opacity-90 cursor-not-allowed bg-gray-50'
        : 'hover:shadow-xl hover:scale-[1.02] md:hover:scale-105 cursor-pointer'
    } ${colors.border} ${isDisabled ? 'border-gray-200' : colors.hover} border-2 overflow-hidden relative`}>
        {/* 预览图片区域 */}
        <div className="relative h-32 sm:h-40 md:h-48 bg-gray-100 overflow-hidden">
          {category.previewImage ? (
            <Image
              src={category.previewImage}
              alt={`${category.displayName} preview`}
              fill
              className={`object-cover transition-transform duration-300 ${
                isDisabled ? '' : 'group-hover:scale-110'
              }`}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              onError={(e) => {
                // 如果图片加载失败，显示默认内容
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
              <div className="text-center">
                <div className={`w-16 h-16 mx-auto mb-2 rounded-full flex items-center justify-center ${colors.bg} ${colors.text}`}>
                  {getCategoryIcon(category.id)}
                </div>
                <p className="text-sm text-gray-500">Preview</p>
              </div>
            </div>
          )}

          {/* 模板数量徽章和Coming Soon标签 */}
          <div className="absolute top-3 right-3 z-20 flex flex-col gap-2">
            {isDisabled && (
              <Badge variant="outline" className="text-xs text-orange-600 border-orange-300 bg-orange-50/90">
                Coming Soon
              </Badge>
            )}
            <Badge variant="secondary" className={`${
              isDisabled ? 'bg-gray-200/90 text-gray-500' : 'bg-white/90 text-gray-700'
            }`}>
              {category.templateCount || 0} templates
            </Badge>
          </div>
        </div>

        <CardHeader className="pb-2 px-3 sm:px-6">
          <div className="flex items-center justify-between mb-2">
            <div className={`p-1.5 sm:p-2 rounded-lg ${
              isDisabled ? 'bg-gray-200 text-gray-500' : `${colors.bg} ${colors.text}`
            } ${
              isDisabled ? '' : 'group-hover:scale-110'
            } transition-transform`}>
              {getCategoryIcon(category.id)}
            </div>
            {!isDisabled && (
              <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all" />
            )}
          </div>

          <CardTitle className={`text-base sm:text-lg ${
            isDisabled ? 'text-gray-500' : `group-hover:${colors.text}`
          } transition-colors leading-tight`}>
            {category.displayName.replace(' Templates', '')}
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-0 px-3 sm:px-6 pb-3 sm:pb-6">
          <p className={`text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4 line-clamp-2 ${
            isDisabled ? 'text-gray-400' : 'text-gray-600'
          }`}>
            {category.description}
          </p>

          <div className="flex items-center justify-between">
            <div className={`text-xs ${isDisabled ? 'text-gray-400' : 'text-gray-500'}`}>
              {category.defaultSize === 'landscape' ? 'Landscape' : 'Portrait'} format
            </div>
            {!isDisabled && (
              <Button
                variant="ghost"
                size="sm"
                className={`${colors.text} hover:${colors.bg} transition-colors text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 h-auto min-h-[44px] touch-manipulation`}
              >
                <span className="hidden sm:inline">Browse Templates</span>
                <span className="sm:hidden">Browse</span>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );

  // 根据是否禁用来决定是否包装在Link中
  if (isDisabled) {
    return (
      <div className={`${className} min-h-[44px] touch-manipulation`}>
        {cardContent}
      </div>
    );
  }

  return (
    <Link
      href={`/certificate-templates/${category.urlSlug}/`}
      className={`group block ${className} min-h-[44px] touch-manipulation`}
    >
      {cardContent}
    </Link>
  );
}
