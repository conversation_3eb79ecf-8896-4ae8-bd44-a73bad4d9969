'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { CertificateTemplate, TemplateCategory } from '@/types/certificate';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import BreadcrumbNavigation from './BreadcrumbNavigation';
import CertificateMaker from './CertificateMaker';
import { Download, Zap, Star, CheckCircle, Users, Award, TrendingUp, FileSpreadsheet } from 'lucide-react';

interface CategoryDetailPageProps {
  category: TemplateCategory;
  templates: CertificateTemplate[];
  defaultTemplate: CertificateTemplate | null;
}

// SEO优化的分类内容配置
const getCategoryContent = (categorySlug: string) => {
  const contentMap = {
    'achievement-certificates': {
      title: 'Achievement Certificate Templates',
      subtitle: 'Professional Recognition Made Easy',
      description: 'Create stunning achievement certificates that celebrate success and recognize outstanding performance. Our professionally designed templates are perfect for employee recognition, academic awards, business achievements, and milestone celebrations.',
      benefits: [
        'Professional Design: Elegant layouts that convey prestige and credibility',
        'Instant Recognition: Immediate acknowledgment of accomplishments and achievements',
        'Versatile Usage: Perfect for corporate awards, academic honors, and personal milestones',
        'High-Quality Output: Print-ready PDFs with crisp text and professional formatting',
        'Easy Customization: Simple form-based editing with real-time preview',
        'Time-Saving: Generate professional certificates in under 2 minutes'
      ],
      useCases: [
        'Employee of the Month Awards',
        'Sales Performance Recognition',
        'Academic Excellence Awards',
        'Project Completion Milestones',
        'Leadership Recognition',
        'Customer Service Excellence',
        'Innovation and Creativity Awards',
        'Long Service Recognition'
      ],
      features: [
        'Multiple professional templates with elegant designs',
        'Customizable text fields for recipient name, achievement details, and dates',
        'High-resolution PDF output suitable for printing and digital sharing',
        'Mobile-responsive design for creation on any device',
        'Instant download with no registration required',
        'Professional typography and color schemes'
      ],
      keywords: 'achievement certificates, employee recognition, professional awards, business certificates, academic achievement, milestone recognition, corporate awards, success celebration'
    },
    'completion-certificates': {
      title: 'Completion Certificate Templates',
      subtitle: 'Document Learning Achievements',
      description: 'Generate professional completion certificates for courses, training programs, workshops, and educational achievements. Our templates are designed to provide official documentation of learning milestones and skill development.',
      benefits: [
        'Educational Credibility: Official-looking certificates that validate learning achievements',
        'Professional Documentation: Proper certification for training programs and courses',
        'Motivation Enhancement: Tangible recognition that encourages continued learning',
        'Compliance Ready: Suitable for corporate training and educational requirements',
        'Instant Generation: Quick creation for immediate distribution to participants',
        'Cost-Effective: Free alternative to expensive certificate printing services'
      ],
      useCases: [
        'Online Course Completion',
        'Corporate Training Programs',
        'Workshop Attendance',
        'Skill Development Courses',
        'Safety Training Certification',
        'Professional Development Programs',
        'Continuing Education Credits',
        'Certification Program Completion'
      ],
      features: [
        'Clean, professional designs suitable for educational institutions',
        'Customizable course details, completion dates, and instructor signatures',
        'Multiple layout options for different types of training programs',
        'High-quality PDF generation for official documentation',
        'Easy bulk creation for multiple participants',
        'Professional fonts and layouts that convey educational authority'
      ],
      keywords: 'completion certificates, course completion, training certificates, educational certificates, professional development, skill certification, learning documentation, training completion'
    },
    'participation-certificates': {
      title: 'Participation Certificate Templates',
      subtitle: 'Appreciate Engagement and Involvement',
      description: 'Create beautiful participation certificates for events, workshops, seminars, and conferences. Show appreciation for participants\' time, engagement, and contribution to your events and programs.',
      benefits: [
        'Participant Appreciation: Show gratitude for attendance and engagement',
        'Event Documentation: Provide proof of participation for professional development',
        'Networking Value: Certificates serve as conversation starters and networking tools',
        'Brand Recognition: Reinforce your event or organization\'s professional image',
        'Motivation Boost: Encourage future participation and engagement',
        'Professional Keepsake: Lasting reminder of valuable experiences and learning'
      ],
      useCases: [
        'Conference Attendance',
        'Workshop Participation',
        'Seminar Involvement',
        'Webinar Attendance',
        'Community Event Participation',
        'Volunteer Recognition',
        'Panel Discussion Participation',
        'Networking Event Attendance'
      ],
      features: [
        'Elegant designs that reflect the importance of participation',
        'Customizable event details, dates, and organizer information',
        'Multiple template styles for different types of events',
        'Professional layouts suitable for corporate and academic events',
        'Easy customization for event branding and themes',
        'High-quality output for both digital sharing and printing'
      ],
      keywords: 'participation certificates, event certificates, workshop participation, conference attendance, seminar certificates, volunteer recognition, community involvement, professional participation'
    },
    'excellence-certificates': {
      title: 'Excellence Certificate Templates',
      subtitle: 'Celebrate Outstanding Performance',
      description: 'Recognize exceptional achievements and outstanding performance with our premium excellence certificate templates. Perfect for celebrating individuals who go above and beyond expectations and demonstrate exceptional quality in their work.',
      benefits: [
        'Premium Recognition: Distinguished certificates for exceptional achievements',
        'Motivation Driver: Powerful incentive for continued excellence and high performance',
        'Quality Assurance: Recognize and reinforce standards of excellence',
        'Competitive Advantage: Differentiate top performers and exceptional contributors',
        'Professional Prestige: Elegant designs that convey the significance of the achievement',
        'Lasting Impact: Memorable recognition that recipients will treasure and display'
      ],
      useCases: [
        'Outstanding Performance Awards',
        'Quality Excellence Recognition',
        'Innovation and Creativity Awards',
        'Leadership Excellence',
        'Customer Service Excellence',
        'Academic Excellence Awards',
        'Professional Excellence Recognition',
        'Team Excellence Awards'
      ],
      features: [
        'Premium template designs with sophisticated layouts and typography',
        'Customizable excellence criteria and achievement descriptions',
        'Professional color schemes that convey prestige and importance',
        'High-impact visual elements that emphasize the significance of the award',
        'Multiple design options for different types of excellence recognition',
        'Superior print quality for framing and display purposes'
      ],
      keywords: 'excellence certificates, outstanding performance, quality awards, premium recognition, professional excellence, leadership awards, innovation recognition, superior achievement'
    }
  };

  return contentMap[categorySlug as keyof typeof contentMap] || contentMap['achievement-certificates'];
};

export default function CategoryDetailPage({ category, templates, defaultTemplate }: CategoryDetailPageProps) {
  // 支持模板选择，默认使用第一个模板
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(defaultTemplate || templates[0] || null);
  const categoryContent = getCategoryContent(category.urlSlug);
  // 键盘导航支持
  const handleKeyNavigation = useCallback((event: KeyboardEvent) => {
    if (!selectedTemplate || templates.length <= 1) return;

    const currentIndex = templates.findIndex(t => t.id === selectedTemplate.id);

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : templates.length - 1;
        setSelectedTemplate(templates[prevIndex]);
        break;
      case 'ArrowRight':
        event.preventDefault();
        const nextIndex = currentIndex < templates.length - 1 ? currentIndex + 1 : 0;
        setSelectedTemplate(templates[nextIndex]);
        break;
      case 'Home':
        event.preventDefault();
        setSelectedTemplate(templates[0]);
        break;
      case 'End':
        event.preventDefault();
        setSelectedTemplate(templates[templates.length - 1]);
        break;
    }
  }, [selectedTemplate, templates]);

  // 添加键盘事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyNavigation);
    return () => {
      document.removeEventListener('keydown', handleKeyNavigation);
    };
  }, [handleKeyNavigation]);





  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <BreadcrumbNavigation
        items={[
          { name: 'Home', url: '/' },
          { name: 'Certificate Templates', url: '/certificate-templates/' },
          { name: category.displayName, url: `/certificate-templates/${category.urlSlug}/`, current: true }
        ]}
      />

      {/* 页面头部 */}
      <div className="mb-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {categoryContent.title}
          </h1>
          <h2 className="text-2xl text-blue-600 font-semibold mb-6">
            {categoryContent.subtitle}
          </h2>
          <p className="text-xl text-gray-600 max-w-7xl mx-auto leading-relaxed">
            {categoryContent.description}
          </p>
        </div>
      </div>

      {/* 模板选择器 - 只对横向模板显示 */}
      {templates.length > 0 && selectedTemplate?.orientation === 'landscape' && (
        <div className="mb-16">
          {/* <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Template
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Select from our professionally designed {categoryContent.title.toLowerCase()} to get started.
            </p>
          </div> */}

          {/* 横向模板的展示方式 */}
          {(() => {
            const shouldShowCarousel = templates.length > 5;

            if (!shouldShowCarousel) {
              // 直接展示所有横向模板（1-5个）
              return (
                <div className="flex justify-center px-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 max-w-7xl mx-auto">{/* 主要横向展示，一行5个 */}
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                          selectedTemplate?.id === template.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        } w-full touch-manipulation min-h-[44px]`}
                        onClick={() => setSelectedTemplate(template)}
                      >
                        <div className="p-2">
                          <div className="bg-gray-50 rounded-lg mb-2 overflow-hidden aspect-[4/3]">
                            <img
                              src={template.preview}
                              alt={template.displayName}
                              className="w-full h-full object-contain p-1"
                              draggable={false}
                            />
                          </div>
                          {/* 模板名称 - 简洁设计 */}
                          <div className="text-center">
                            <h3 className="font-medium text-xs text-gray-900 truncate">
                              {template.displayName}
                            </h3>
                          </div>
                          {selectedTemplate?.id === template.id && (
                            <div className="absolute top-2 right-2">
                              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                <CheckCircle className="w-3 h-3 text-white" />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            } else {
              // 使用轮播组件（超过5个模板时）
              return (
                <div className="relative max-w-8xl mx-auto px-4">

                  {(() => {
                    const currentIndex = templates.findIndex(t => t.id === selectedTemplate?.id);
                    const templatesPerPage = 5; // 固定每页显示5个模板
                    const currentPage = Math.floor(currentIndex / templatesPerPage);
                    const totalPages = Math.ceil(templates.length / templatesPerPage);
                    const startIndex = currentPage * templatesPerPage;
                    const endIndex = Math.min(startIndex + templatesPerPage, templates.length);
                    const visibleTemplates = templates.slice(startIndex, endIndex);

                    const canGoPrev = currentPage > 0;
                    const canGoNext = currentPage < totalPages - 1;

                    const goToPrevPage = () => {
                      if (canGoPrev) {
                        const newIndex = (currentPage - 1) * templatesPerPage;
                        setSelectedTemplate(templates[newIndex]);
                      }
                    };

                    const goToNextPage = () => {
                      if (canGoNext) {
                        const newIndex = (currentPage + 1) * templatesPerPage;
                        setSelectedTemplate(templates[newIndex]);
                      }
                    };

                    return (
                      <div className="flex items-center justify-center space-x-4">
                        {/* 左导航按钮 */}
                        <button
                          onClick={goToPrevPage}
                          disabled={!canGoPrev}
                          className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                            canGoPrev
                              ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                              : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                          }`}
                          aria-label="Previous page"
                        >
                          <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>

                        {/* 模板网格 - 主要横向展示，一行5个 */}
                        <div className="flex-1 overflow-hidden">
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 max-w-7xl mx-auto">
                            {visibleTemplates.map((template) => (
                              <div
                                key={template.id}
                                className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                                  selectedTemplate?.id === template.id
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                } w-full touch-manipulation min-h-[44px]`}
                                onClick={() => setSelectedTemplate(template)}
                              >
                                <div className="p-2">
                                  <div className="bg-gray-50 rounded-lg mb-2 overflow-hidden aspect-[4/3]">
                                    <img
                                      src={template.preview}
                                      alt={template.displayName}
                                      className="w-full h-full object-contain p-1"
                                      draggable={false}
                                    />
                                  </div>
                                  {/* 模板名称 - 简洁设计 */}
                                  <div className="text-center">
                                    <h3 className="font-medium text-xs text-gray-900 truncate">
                                      {template.displayName}
                                    </h3>
                                  </div>
                                  {selectedTemplate?.id === template.id && (
                                    <div className="absolute top-2 right-2">
                                      <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                        <CheckCircle className="w-3 h-3 text-white" />
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* 右导航按钮 */}
                        <button
                          onClick={goToNextPage}
                          disabled={!canGoNext}
                          className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                            canGoNext
                              ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                              : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                          }`}
                          aria-label="Next page"
                        >
                          <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    );
                  })()}

                  {/* 页面指示器 */}
                  <div className="mt-6 text-center">
                    {(() => {
                      const currentIndex = templates.findIndex(t => t.id === selectedTemplate?.id);
                      const templatesPerPage = 5;
                      const currentPage = Math.floor(currentIndex / templatesPerPage);
                      const totalPages = Math.ceil(templates.length / templatesPerPage);

                      if (totalPages <= 1) return null;

                      return (
                        <div className="flex justify-center items-center space-x-2">
                          {/* <span className="text-sm text-gray-600">
                            Page {currentPage + 1} of {totalPages}
                          </span> */}
                          <div className="flex space-x-1">
                            {Array.from({ length: totalPages }, (_, i) => (
                              <button
                                key={i}
                                onClick={() => {
                                  const newIndex = i * templatesPerPage;
                                  setSelectedTemplate(templates[newIndex]);
                                }}
                                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                  i === currentPage
                                    ? 'bg-blue-500 scale-125'
                                    : 'bg-gray-300 hover:bg-gray-400'
                                }`}
                                aria-label={`Go to page ${i + 1}`}
                              />
                            ))}
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>
              );
            }
          })()}
        </div>
      )}



      {/* 证书生成器 */}
      <div className="mb-16">
        {selectedTemplate ? (
          <div>
            {/* <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Create Your Certificate
              </h2>
            </div> */}

            <div data-certificate-maker>
              <CertificateMaker
                selectedTemplate={selectedTemplate}
                templates={templates}
                onTemplateChange={setSelectedTemplate}
              />
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                No Templates Available
              </h2>
              <p className="text-gray-600 mb-6">
                This category doesn&apos;t have any templates yet. Please check back later or try another category.
              </p>
              <Link href="/certificate-templates/">
                <Button>
                  Browse Other Categories
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* SEO优化内容部分 */}
      <div className="space-y-16">
        {/* 主要优势 */}
        <section>
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Why Choose Our {categoryContent.title}?
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {categoryContent.benefits.map((benefit, index) => {
              const [title, description] = benefit.split(': ');
              return (
                <Card key={index} className="border-2 hover:border-blue-200 transition-colors">
                  <CardHeader>
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                      <CheckCircle className="w-6 h-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">{title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* 使用场景 */}
        <section className="bg-gray-50 rounded-2xl p-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Perfect for These Scenarios
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categoryContent.useCases.map((useCase, index) => (
              <div key={index} className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{useCase}</h3>
              </div>
            ))}
          </div>
        </section>

        {/* 主要特性 */}
        <section>
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Key Features & Capabilities
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {categoryContent.features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Star className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-gray-700 leading-relaxed">{feature}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* 快速开始 */}
        <section className="bg-blue-50 rounded-2xl p-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-4xl mx-auto">
            Create your professional {categoryContent.title.toLowerCase()} in just a few minutes.
            No design experience required - just fill in your details and download.
          </p>
          <div className="flex justify-center items-center space-x-8 text-sm text-gray-600 mb-8">
            <div className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-blue-600" />
              <span>2-minute creation</span>
            </div>
            <div className="flex items-center space-x-2">
              <Download className="w-5 h-5 text-green-600" />
              <span>Instant PDF download</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-purple-600" />
              <span>Professional quality</span>
            </div>
          </div>
          <Button size="lg" className="text-lg px-8 py-4" onClick={() => {
            const certificateSection = document.querySelector('[data-certificate-maker]');
            certificateSection?.scrollIntoView({ behavior: 'smooth' });
          }}>
            Start Creating Now
            <TrendingUp className="w-5 h-5 ml-2" />
          </Button>
        </section>
      </div>
    </div>
  );
}
