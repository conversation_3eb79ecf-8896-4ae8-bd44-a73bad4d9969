/**
 * PDF生成性能测试工具
 * 用于测试和验证PDF生成的性能优化效果
 */

import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { generatePDFOnServer } from '@/lib/server-pdf-client';

interface PerformanceTestResult {
  testName: string;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  successCount: number;
  errorCount: number;
  throughput: number; // 每秒生成数量
  memoryUsage?: {
    before: number;
    after: number;
    peak: number;
  };
}

/**
 * PDF性能测试器
 */
export class PDFPerformanceTest {
  private results: PerformanceTestResult[] = [];

  /**
   * 测试单个PDF生成性能
   */
  async testSingleGeneration(
    template: CertificateTemplate,
    data: CertificateData,
    iterations: number = 10
  ): Promise<PerformanceTestResult> {
    console.log(`🧪 Testing single PDF generation (${iterations} iterations)...`);
    
    const times: number[] = [];
    let successCount = 0;
    let errorCount = 0;
    
    const memoryBefore = this.getMemoryUsage();
    let memoryPeak = memoryBefore;
    
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      try {
        const iterationStart = Date.now();
        
        const result = await generatePDFOnServer(template, {
          ...data,
          recipientName: `${data.recipientName}_${i + 1}` // 确保每次生成不同
        });
        
        const iterationTime = Date.now() - iterationStart;
        
        if (result.success) {
          times.push(iterationTime);
          successCount++;
          console.log(`✅ Iteration ${i + 1}: ${iterationTime}ms`);
        } else {
          errorCount++;
          console.log(`❌ Iteration ${i + 1}: Failed`);
        }
        
        // 监控内存使用
        const currentMemory = this.getMemoryUsage();
        if (currentMemory > memoryPeak) {
          memoryPeak = currentMemory;
        }
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Iteration ${i + 1}: Error`, error);
      }
    }
    
    const totalTime = Date.now() - startTime;
    const memoryAfter = this.getMemoryUsage();
    
    const result: PerformanceTestResult = {
      testName: 'Single PDF Generation',
      totalTime,
      averageTime: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
      minTime: times.length > 0 ? Math.min(...times) : 0,
      maxTime: times.length > 0 ? Math.max(...times) : 0,
      successCount,
      errorCount,
      throughput: successCount / (totalTime / 1000),
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        peak: memoryPeak
      }
    };
    
    this.results.push(result);
    this.printTestResult(result);
    
    return result;
  }

  /**
   * 测试并发PDF生成性能
   */
  async testConcurrentGeneration(
    template: CertificateTemplate,
    baseData: CertificateData,
    concurrency: number = 5,
    totalCount: number = 20
  ): Promise<PerformanceTestResult> {
    console.log(`🧪 Testing concurrent PDF generation (${concurrency} concurrent, ${totalCount} total)...`);
    
    const times: number[] = [];
    let successCount = 0;
    let errorCount = 0;
    
    const memoryBefore = this.getMemoryUsage();
    let memoryPeak = memoryBefore;
    
    const startTime = Date.now();
    
    // 创建任务队列
    const tasks: Promise<void>[] = [];
    
    for (let i = 0; i < totalCount; i++) {
      const task = this.generateWithTiming(template, {
        ...baseData,
        recipientName: `${baseData.recipientName}_concurrent_${i + 1}`
      }, i + 1).then(({ success, time }) => {
        if (success) {
          times.push(time);
          successCount++;
        } else {
          errorCount++;
        }
        
        // 监控内存使用
        const currentMemory = this.getMemoryUsage();
        if (currentMemory > memoryPeak) {
          memoryPeak = currentMemory;
        }
      });
      
      tasks.push(task);
      
      // 控制并发数
      if (tasks.length >= concurrency) {
        await Promise.race(tasks);
        // 移除已完成的任务
        for (let j = tasks.length - 1; j >= 0; j--) {
          if (await this.isPromiseResolved(tasks[j])) {
            tasks.splice(j, 1);
          }
        }
      }
    }
    
    // 等待所有任务完成
    await Promise.allSettled(tasks);
    
    const totalTime = Date.now() - startTime;
    const memoryAfter = this.getMemoryUsage();
    
    const result: PerformanceTestResult = {
      testName: `Concurrent PDF Generation (${concurrency} concurrent)`,
      totalTime,
      averageTime: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
      minTime: times.length > 0 ? Math.min(...times) : 0,
      maxTime: times.length > 0 ? Math.max(...times) : 0,
      successCount,
      errorCount,
      throughput: successCount / (totalTime / 1000),
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        peak: memoryPeak
      }
    };
    
    this.results.push(result);
    this.printTestResult(result);
    
    return result;
  }

  /**
   * 生成PDF并计时
   */
  private async generateWithTiming(
    template: CertificateTemplate,
    data: CertificateData,
    index: number
  ): Promise<{ success: boolean; time: number }> {
    try {
      const start = Date.now();
      const result = await generatePDFOnServer(template, data);
      const time = Date.now() - start;
      
      console.log(`✅ Concurrent task ${index}: ${time}ms`);
      return { success: result.success, time };
    } catch (error) {
      console.error(`❌ Concurrent task ${index}: Error`, error);
      return { success: false, time: 0 };
    }
  }

  /**
   * 检查Promise是否已解决
   */
  private async isPromiseResolved(promise: Promise<any>): Promise<boolean> {
    try {
      await Promise.race([promise, new Promise(resolve => setTimeout(resolve, 0))]);
      return true;
    } catch {
      return true; // 即使失败也算已解决
    }
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * 打印测试结果
   */
  private printTestResult(result: PerformanceTestResult): void {
    console.log('\n📊 Performance Test Result:');
    console.log(`Test: ${result.testName}`);
    console.log(`Total Time: ${result.totalTime}ms`);
    console.log(`Average Time: ${Math.round(result.averageTime)}ms`);
    console.log(`Min Time: ${result.minTime}ms`);
    console.log(`Max Time: ${result.maxTime}ms`);
    console.log(`Success: ${result.successCount}, Errors: ${result.errorCount}`);
    console.log(`Throughput: ${result.throughput.toFixed(2)} PDFs/second`);
    
    if (result.memoryUsage) {
      console.log(`Memory - Before: ${Math.round(result.memoryUsage.before / 1024 / 1024)}MB`);
      console.log(`Memory - After: ${Math.round(result.memoryUsage.after / 1024 / 1024)}MB`);
      console.log(`Memory - Peak: ${Math.round(result.memoryUsage.peak / 1024 / 1024)}MB`);
    }
    console.log('─'.repeat(50));
  }

  /**
   * 获取所有测试结果
   */
  getResults(): PerformanceTestResult[] {
    return this.results;
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    if (this.results.length === 0) {
      return 'No test results available.';
    }

    let report = '\n📈 PDF Generation Performance Report\n';
    report += '='.repeat(50) + '\n\n';

    this.results.forEach((result, index) => {
      report += `${index + 1}. ${result.testName}\n`;
      report += `   Total Time: ${result.totalTime}ms\n`;
      report += `   Average Time: ${Math.round(result.averageTime)}ms\n`;
      report += `   Throughput: ${result.throughput.toFixed(2)} PDFs/second\n`;
      report += `   Success Rate: ${((result.successCount / (result.successCount + result.errorCount)) * 100).toFixed(1)}%\n`;
      
      if (result.memoryUsage) {
        const memoryIncrease = result.memoryUsage.after - result.memoryUsage.before;
        report += `   Memory Increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB\n`;
      }
      
      report += '\n';
    });

    // 性能建议
    report += '💡 Performance Recommendations:\n';
    const avgThroughput = this.results.reduce((sum, r) => sum + r.throughput, 0) / this.results.length;
    
    if (avgThroughput < 1) {
      report += '- Consider increasing server resources or optimizing font loading\n';
    } else if (avgThroughput > 5) {
      report += '- Excellent performance! Consider increasing concurrency for batch operations\n';
    } else {
      report += '- Good performance. Monitor memory usage during large batch operations\n';
    }

    return report;
  }
}

/**
 * 快速性能测试函数
 */
export async function quickPerformanceTest(
  template: CertificateTemplate,
  data: CertificateData
): Promise<void> {
  const tester = new PDFPerformanceTest();
  
  console.log('🚀 Starting Quick Performance Test...');
  
  // 测试单个生成
  await tester.testSingleGeneration(template, data, 5);
  
  // 测试并发生成
  await tester.testConcurrentGeneration(template, data, 3, 10);
  
  // 打印报告
  console.log(tester.generateReport());
}
