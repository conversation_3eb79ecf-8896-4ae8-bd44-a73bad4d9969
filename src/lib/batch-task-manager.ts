import {
  BatchGenerationT<PERSON>,
  Batch<PERSON><PERSON><PERSON>tatus,
  BatchGenerationError,
  BatchGenerationProgress,
  BatchCertificateData
} from '@/types/certificate';
import fs from 'fs';
import { promises as fsPromises } from 'fs';
import path from 'path';

/**
 * Batch Task Manager
 * Manages the lifecycle of batch generation tasks
 * Uses file system persistent storage to prevent data loss during hot reloads
 */
export class BatchTaskManager {
  private static tasks = new Map<string, BatchGenerationTask>();
  private static readonly TASK_CLEANUP_DELAY = 24 * 60 * 60 * 1000; // 24 hours cleanup delay
  private static readonly COMPLETED_TASK_KEEP_ALIVE = 2 * 60 * 60 * 1000; // Keep completed tasks for 2 hours
  private static readonly TASK_TIMEOUT = 30 * 60 * 1000; // 30 minutes task timeout
  private static readonly STORAGE_FILE = path.join(process.cwd(), 'temp', 'batch-tasks.json');
  private static readonly TEMP_DIR = path.join(process.cwd(), 'temp', 'batch-pdfs');
  private static initialized = false;
  private static taskTimeouts = new Map<string, NodeJS.Timeout>();
  private static cleanupInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize task manager, load tasks from persistent storage
   */
  private static initialize(): void {
    if (this.initialized) return;

    try {
      // Ensure storage directory exists
      const storageDir = path.dirname(this.STORAGE_FILE);
      if (!fs.existsSync(storageDir)) {
        fs.mkdirSync(storageDir, { recursive: true });
      }

      // Load stored tasks
      if (fs.existsSync(this.STORAGE_FILE)) {
        const data = fs.readFileSync(this.STORAGE_FILE, 'utf8');
        const tasksData = JSON.parse(data);

        // Restore tasks, convert date strings to Date objects
        for (const [taskId, taskData] of Object.entries(tasksData)) {
          const task = taskData as any;
          task.createdAt = new Date(task.createdAt);
          task.updatedAt = new Date(task.updatedAt);
          this.tasks.set(taskId, task as BatchGenerationTask);
        }

        console.log(`🔄 Restored ${this.tasks.size} tasks from persistent storage`);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load tasks from storage:', error);
    }

    this.initialized = true;

    // 启动定时清理任务
    this.startPeriodicCleanup();
  }

  /**
   * Save tasks to persistent storage
   */
  private static saveToStorage(): void {
    // Use async operation to prevent blocking
    setImmediate(async () => {
      try {
        const tasksData = Object.fromEntries(this.tasks.entries());
        await fsPromises.writeFile(this.STORAGE_FILE, JSON.stringify(tasksData, null, 2), 'utf8');
      } catch (error) {
        console.warn('⚠️ Failed to save tasks to storage:', error);
      }
    });
  }



  /**
   * Create new batch task
   */
  static createTask(
    templateId: string,
    certificates: BatchCertificateData[]
  ): BatchGenerationTask {
    this.initialize(); // Ensure initialized

    const taskId = this.generateTaskId();
    const now = new Date();

    const task: BatchGenerationTask = {
      id: taskId,
      templateId,
      totalCount: certificates.length,
      completedCount: 0,
      failedCount: 0,
      status: BatchTaskStatus.PENDING,
      createdAt: now,
      updatedAt: now,
      errors: []
    };

    this.tasks.set(taskId, task);
    this.saveToStorage(); // Save to persistent storage

    // Set up task timeout
    const timeoutId = setTimeout(() => {
      const currentTask = this.tasks.get(taskId);
      if (currentTask && (currentTask.status === BatchTaskStatus.PENDING || currentTask.status === BatchTaskStatus.PROCESSING)) {
        console.warn(`⏰ Task ${taskId} timed out after ${this.TASK_TIMEOUT / 1000} seconds`);
        currentTask.status = BatchTaskStatus.FAILED;
        currentTask.updatedAt = new Date();
        this.tasks.set(taskId, currentTask);
        this.saveToStorage();
      }
      this.taskTimeouts.delete(taskId);
    }, this.TASK_TIMEOUT);

    this.taskTimeouts.set(taskId, timeoutId);

    // Set up automatic cleanup - only cleanup incomplete tasks
    setTimeout(() => {
      this.cleanupTask(taskId, false); // false = don't force cleanup completed tasks
    }, this.TASK_CLEANUP_DELAY);

    console.log(`✅ Created task ${taskId} with ${certificates.length} certificates`);
    return task;
  }

  /**
   * Get task information
   */
  static getTask(taskId: string): BatchGenerationTask | undefined {
    this.initialize(); // Ensure initialized
    return this.tasks.get(taskId);
  }

  /**
   * Update task status
   */
  static updateTaskStatus(taskId: string, status: BatchTaskStatus): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = status;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
    }
  }

  /**
   * Update task progress
   */
  static updateTaskProgress(
    taskId: string,
    completedCount: number,
    failedCount: number = 0
  ): void {
    this.initialize(); // Ensure initialized

    const task = this.tasks.get(taskId);
    if (task) {
      const wasCompleted = task.status === BatchTaskStatus.COMPLETED;

      task.completedCount = completedCount;
      task.failedCount = failedCount;
      task.updatedAt = new Date();

      // If all items are processed, update status
      if (completedCount + failedCount >= task.totalCount) {
        task.status = BatchTaskStatus.COMPLETED; // Mark as completed regardless of errors

        // If this is the first time marked as completed, set protective delayed cleanup
        if (!wasCompleted) {
          console.log(`🛡️ Task ${taskId} completed via progress update, setting protection`);
          setTimeout(() => {
            this.cleanupTask(taskId, true);
          }, this.COMPLETED_TASK_KEEP_ALIVE);
        }
      } else if (task.status === BatchTaskStatus.PENDING) {
        task.status = BatchTaskStatus.PROCESSING; // Update status when starting processing
      }

      this.tasks.set(taskId, task);
      this.saveToStorage(); // Save to persistent storage
    }
  }

  /**
   * Add error information
   */
  static addTaskError(taskId: string, error: BatchGenerationError): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.errors.push(error);
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
    }
  }

  /**
   * Set download URL and complete task
   */
  static setDownloadUrl(taskId: string, downloadUrl: string): void {
    this.initialize(); // Ensure initialized

    const task = this.tasks.get(taskId);
    if (task) {
      task.downloadUrl = downloadUrl;
      task.status = BatchTaskStatus.COMPLETED; // Ensure status is completed
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);

      // Synchronous save for immediate consistency
      try {
        const tasksData = Object.fromEntries(this.tasks.entries());
        fs.writeFileSync(this.STORAGE_FILE, JSON.stringify(tasksData, null, 2));
      } catch (error) {
        console.warn('⚠️ Failed to save task completion to storage:', error);
      }

      console.log(`✅ Task ${taskId} marked as completed with download URL: ${downloadUrl}`);
      console.log(`⏰ Task ${taskId} will be kept alive for ${this.COMPLETED_TASK_KEEP_ALIVE / 1000 / 60} minutes`);

      // Force refresh task status in memory
      this.forceRefreshTask(taskId);
    } else {
      console.error(`❌ Task ${taskId} not found when setting download URL`);
    }
  }

  /**
   * Force refresh task status (ensure status sync)
   */
  private static forceRefreshTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task) {
      // Reset task to ensure status sync
      this.tasks.set(taskId, { ...task });
      console.log(`🔄 Force refreshed task ${taskId} status: ${task.status}`);
    }
  }

  /**
   * Complete task (success or failure) and cleanup timeout
   */
  static completeTask(taskId: string, success: boolean = true): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = success ? BatchTaskStatus.COMPLETED : BatchTaskStatus.FAILED;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);

      // Clear task timeout since task is complete
      const timeoutId = this.taskTimeouts.get(taskId);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.taskTimeouts.delete(taskId);
      }

      console.log(`✅ Task ${taskId} completed with status: ${task.status}`);

      // Note: Delayed cleanup will be set in setDownloadUrl to avoid duplicate setup
    }
  }

  /**
   * Mark task as downloaded (can be cleaned up immediately)
   */
  static markTaskAsDownloaded(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && task.status === BatchTaskStatus.COMPLETED) {
      console.log(`📥 Task ${taskId} marked as downloaded, scheduling cleanup`);
      // Cleanup after 5 minutes delay, give users some buffer time
      setTimeout(() => {
        this.cleanupTask(taskId, true);
      }, 5 * 60 * 1000);
    }
  }

  /**
   * Cancel task
   */
  static cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (task && (task.status === BatchTaskStatus.PENDING || task.status === BatchTaskStatus.PROCESSING)) {
      task.status = BatchTaskStatus.CANCELLED;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
      return true;
    }
    return false;
  }

  /**
   * Get task progress information
   */
  static getTaskProgress(taskId: string): BatchGenerationProgress | null {
    this.initialize(); // Ensure initialized

    // Force refresh from storage to ensure latest status
    try {
      if (fs.existsSync(this.STORAGE_FILE)) {
        const data = fs.readFileSync(this.STORAGE_FILE, 'utf8');
        const tasksData = JSON.parse(data);

        // Update existing tasks with latest data from storage
        for (const [taskId, taskData] of Object.entries(tasksData)) {
          const task = taskData as any;
          task.createdAt = new Date(task.createdAt);
          task.updatedAt = new Date(task.updatedAt);
          this.tasks.set(taskId, task as BatchGenerationTask);
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to refresh tasks from storage:', error);
    }

    const task = this.tasks.get(taskId);
    if (!task) {
      console.warn(`⚠️ Task ${taskId} not found in task manager (total tasks: ${this.tasks.size})`);
      return null;
    }

    console.log(`📊 Task ${taskId} status: ${task.status}, progress: ${task.completedCount + task.failedCount}/${task.totalCount}`);

    const progress = task.totalCount > 0
      ? Math.round(((task.completedCount + task.failedCount) / task.totalCount) * 100)
      : 0;

    return {
      taskId: task.id,
      status: task.status,
      totalCount: task.totalCount,
      completedCount: task.completedCount,
      failedCount: task.failedCount,
      progress,
      errors: task.errors,
      estimatedTimeRemaining: this.calculateEstimatedTime(task)
    };
  }

  /**
   * Clean up expired tasks
   */
  private static cleanupTask(taskId: string, forceCleanup: boolean = false): void {
    this.initialize(); // Ensure initialized

    const task = this.tasks.get(taskId);
    if (!task) return;

    const now = Date.now();
    const taskAge = now - task.createdAt.getTime();
    const completedAge = task.updatedAt ? now - task.updatedAt.getTime() : 0;

    // If force cleanup, or task failed/cancelled, cleanup immediately
    if (forceCleanup || task.status === BatchTaskStatus.FAILED || task.status === BatchTaskStatus.CANCELLED) {
      this.tasks.delete(taskId);
      this.saveToStorage(); // Save to persistent storage
      console.log(`🗑️ Cleaned up task: ${taskId} (status: ${task.status})`);
      return;
    }

    // If task is completed, check if it exceeds keep-alive time
    if (task.status === BatchTaskStatus.COMPLETED) {
      if (completedAge > this.COMPLETED_TASK_KEEP_ALIVE) {
        this.tasks.delete(taskId);
        this.saveToStorage(); // Save to persistent storage
        console.log(`🗑️ Cleaned up completed task: ${taskId} (completed ${Math.round(completedAge / 1000 / 60)} minutes ago)`);
      } else {
        console.log(`⏰ Keeping completed task: ${taskId} (will cleanup in ${Math.round((this.COMPLETED_TASK_KEEP_ALIVE - completedAge) / 1000 / 60)} minutes)`);
        // Set delayed cleanup
        setTimeout(() => {
          this.cleanupTask(taskId, true);
        }, this.COMPLETED_TASK_KEEP_ALIVE - completedAge);
      }
      return;
    }

    // If task runs too long, cleanup
    if (taskAge > this.TASK_CLEANUP_DELAY) {
      this.tasks.delete(taskId);
      this.saveToStorage(); // Save to persistent storage
      console.log(`🗑️ Cleaned up long-running task: ${taskId} (age: ${Math.round(taskAge / 1000 / 60)} minutes)`);
    }
  }

  /**
   * Generate unique task ID
   */
  private static generateTaskId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `batch_${timestamp}_${randomStr}`;
  }

  /**
   * Calculate estimated remaining time (seconds)
   */
  private static calculateEstimatedTime(task: BatchGenerationTask): number | undefined {
    if (task.status !== BatchTaskStatus.PROCESSING || task.completedCount === 0) {
      return undefined;
    }

    const elapsedTime = Date.now() - task.createdAt.getTime();
    const avgTimePerItem = elapsedTime / task.completedCount;
    const remainingItems = task.totalCount - task.completedCount - task.failedCount;
    
    return Math.round((avgTimePerItem * remainingItems) / 1000);
  }

  /**
   * Get all active tasks
   */
  static getActiveTasks(): BatchGenerationTask[] {
    return Array.from(this.tasks.values()).filter(task => 
      task.status === BatchTaskStatus.PENDING || 
      task.status === BatchTaskStatus.PROCESSING
    );
  }

  /**
   * Get task statistics
   */
  static getTaskStats(): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    cancelled: number;
  } {
    const tasks = Array.from(this.tasks.values());
    
    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === BatchTaskStatus.PENDING).length,
      processing: tasks.filter(t => t.status === BatchTaskStatus.PROCESSING).length,
      completed: tasks.filter(t => t.status === BatchTaskStatus.COMPLETED).length,
      failed: tasks.filter(t => t.status === BatchTaskStatus.FAILED).length,
      cancelled: tasks.filter(t => t.status === BatchTaskStatus.CANCELLED).length
    };
  }

  /**
   * 启动定时清理任务
   */
  private static startPeriodicCleanup(): void {
    // 如果已经有清理任务在运行，先清除
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // 每30分钟执行一次清理
    this.cleanupInterval = setInterval(() => {
      this.performPeriodicCleanup();
    }, 30 * 60 * 1000);

    console.log('🧹 Started periodic cleanup task (every 30 minutes)');
  }

  /**
   * 执行定时清理
   */
  private static performPeriodicCleanup(): void {
    console.log('🧹 Starting periodic cleanup...');

    try {
      // 清理过期任务
      this.cleanupExpiredTasks();

      // 清理孤立的文件夹
      this.cleanupOrphanedDirectories();

      console.log('✅ Periodic cleanup completed');
    } catch (error) {
      console.error('❌ Periodic cleanup failed:', error);
    }
  }

  /**
   * 清理过期任务
   */
  private static cleanupExpiredTasks(): void {
    const now = Date.now();
    let cleanedCount = 0;
    const tasksToCleanup: string[] = [];

    // 收集需要清理的任务ID
    this.tasks.forEach((task, taskId) => {
      const taskAge = now - task.createdAt.getTime();
      const completedAge = task.updatedAt ? now - task.updatedAt.getTime() : 0;

      // 清理失败或取消的任务
      if (task.status === BatchTaskStatus.FAILED || task.status === BatchTaskStatus.CANCELLED) {
        tasksToCleanup.push(taskId);
        return;
      }

      // 清理超过保活时间的已完成任务
      if (task.status === BatchTaskStatus.COMPLETED && completedAge > this.COMPLETED_TASK_KEEP_ALIVE) {
        tasksToCleanup.push(taskId);
        return;
      }

      // 清理超时的长期运行任务
      if (taskAge > this.TASK_CLEANUP_DELAY) {
        tasksToCleanup.push(taskId);
      }
    });

    // 执行清理
    tasksToCleanup.forEach(taskId => {
      this.tasks.delete(taskId);
      this.cleanupTaskFiles(taskId);
      cleanedCount++;
    });

    if (cleanedCount > 0) {
      console.log(`🗑️ Cleaned up ${cleanedCount} expired tasks`);
      this.saveToStorage();
    }
  }

  /**
   * 清理孤立的目录（没有对应任务的文件夹）
   */
  private static cleanupOrphanedDirectories(): void {
    try {
      if (!fs.existsSync(this.TEMP_DIR)) {
        return;
      }

      const directories = fs.readdirSync(this.TEMP_DIR, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      let cleanedCount = 0;

      for (const dirName of directories) {
        // 检查是否有对应的任务
        if (!this.tasks.has(dirName)) {
          const dirPath = path.join(this.TEMP_DIR, dirName);
          try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            cleanedCount++;
            console.log(`🗑️ Cleaned up orphaned directory: ${dirName}`);
          } catch (error) {
            console.error(`Failed to cleanup directory ${dirName}:`, error);
          }
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} orphaned directories`);
      }
    } catch (error) {
      console.error('Failed to cleanup orphaned directories:', error);
    }
  }

  /**
   * 清理特定任务的文件
   */
  private static cleanupTaskFiles(taskId: string): void {
    try {
      const taskDir = path.join(this.TEMP_DIR, taskId);
      if (fs.existsSync(taskDir)) {
        fs.rmSync(taskDir, { recursive: true, force: true });
        console.log(`🗑️ Cleaned up files for task: ${taskId}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup files for task ${taskId}:`, error);
    }
  }

  /**
   * 停止定时清理任务
   */
  static stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('🛑 Stopped periodic cleanup task');
    }
  }
}
