/**
 * PDF生成性能优化器
 * 提供全面的性能优化解决方案
 */

import { PDFDocument, PDFFont, PDFImage } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { CertificateTemplate } from '@/types/certificate';

interface OptimizedResource {
  font?: PDFFont;
  image?: PDFImage;
  data?: ArrayBuffer;
  timestamp: number;
  size: number;
}

interface PerformanceMetrics {
  fontLoadTime: number;
  imageLoadTime: number;
  pdfGenerationTime: number;
  totalTime: number;
  cacheHitRate: number;
  memoryUsage: number;
}

/**
 * PDF性能优化器类
 */
export class PDFPerformanceOptimizer {
  private static instance: PDFPerformanceOptimizer;
  private resourceCache = new Map<string, OptimizedResource>();
  private fontCache = new Map<string, ArrayBuffer>();
  private imageCache = new Map<string, ArrayBuffer>();
  private preloadedFonts = new Set<string>();
  private preloadedImages = new Set<string>();
  private maxCacheSize = 100 * 1024 * 1024; // 100MB
  private maxAge = 30 * 60 * 1000; // 30分钟
  private metrics: PerformanceMetrics = {
    fontLoadTime: 0,
    imageLoadTime: 0,
    pdfGenerationTime: 0,
    totalTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0
  };

  private constructor() {
    // 定期清理过期缓存
    setInterval(() => this.cleanupExpiredCache(), 5 * 60 * 1000); // 每5分钟清理一次
  }

  static getInstance(): PDFPerformanceOptimizer {
    if (!PDFPerformanceOptimizer.instance) {
      PDFPerformanceOptimizer.instance = new PDFPerformanceOptimizer();
    }
    return PDFPerformanceOptimizer.instance;
  }

  /**
   * 预加载模板资源
   */
  async preloadTemplateResources(template: CertificateTemplate): Promise<void> {
    console.log(`🚀 Preloading resources for template: ${template.displayName}`);
    const startTime = performance.now();

    const promises: Promise<void>[] = [];

    // 预加载字体
    if (template.style?.fonts) {
      const fonts = template.style.fonts;
      if (fonts.name?.family) {
        promises.push(this.preloadFont(fonts.name.family, fonts.name.weight || 400));
      }
      if (fonts.body?.family) {
        promises.push(this.preloadFont(fonts.body.family, fonts.body.weight || 400));
      }
      if (fonts.signature?.family) {
        promises.push(this.preloadFont(fonts.signature.family, fonts.signature.weight || 400));
      }
    }

    // 预加载背景图片
    if (template.backgroundImage) {
      promises.push(this.preloadImage(template.backgroundImage));
    }

    // 预加载其他可能用到的字体
    const commonFonts = ['Inter', 'Dancing Script', 'Playfair Display'];
    for (const font of commonFonts) {
      if (!this.preloadedFonts.has(font)) {
        promises.push(this.preloadFont(font, 400));
      }
    }

    await Promise.allSettled(promises);

    const endTime = performance.now();
    console.log(`✅ Template resources preloaded in ${Math.round(endTime - startTime)}ms`);
  }

  /**
   * 预加载字体
   */
  private async preloadFont(family: string, weight: number = 400): Promise<void> {
    const cacheKey = `font-${family}-${weight}`;
    
    if (this.fontCache.has(cacheKey) || this.preloadedFonts.has(cacheKey)) {
      return;
    }

    try {
      const startTime = performance.now();
      let fontData: ArrayBuffer | undefined = undefined;

      // 尝试加载本地字体
      fontData = await this.loadLocalFont(family, weight);
      
      // 如果本地字体不存在，尝试Google Fonts
      if (!fontData) {
        fontData = await this.loadGoogleFont(family, weight);
      }

      if (fontData) {
        this.fontCache.set(cacheKey, fontData);
        this.preloadedFonts.add(cacheKey);
        
        const loadTime = performance.now() - startTime;
        this.metrics.fontLoadTime += loadTime;
        
        console.log(`📁 Font preloaded: ${family} ${weight} (${Math.round(loadTime)}ms, ${Math.round(fontData.byteLength / 1024)}KB)`);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to preload font: ${family} ${weight}`, error);
    }
  }

  /**
   * 预加载图片
   */
  private async preloadImage(imageUrl: string): Promise<void> {
    const cacheKey = `image-${imageUrl}`;
    
    if (this.imageCache.has(cacheKey) || this.preloadedImages.has(cacheKey)) {
      return;
    }

    try {
      const startTime = performance.now();
      
      // 处理相对路径
      let fullUrl = imageUrl;
      if (imageUrl.startsWith('/')) {
        fullUrl = `${window.location.origin}${imageUrl}`;
      }

      const response = await fetch(fullUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const imageData = await response.arrayBuffer();
      this.imageCache.set(cacheKey, imageData);
      this.preloadedImages.add(cacheKey);
      
      const loadTime = performance.now() - startTime;
      this.metrics.imageLoadTime += loadTime;
      
      console.log(`🖼️ Image preloaded: ${imageUrl} (${Math.round(loadTime)}ms, ${Math.round(imageData.byteLength / 1024)}KB)`);
    } catch (error) {
      console.warn(`⚠️ Failed to preload image: ${imageUrl}`, error);
    }
  }

  /**
   * 获取优化的字体
   */
  async getOptimizedFont(pdfDoc: PDFDocument, family: string, weight: number = 400): Promise<PDFFont | null> {
    const cacheKey = `font-${family}-${weight}`;
    const resourceKey = `pdf-font-${family}-${weight}-${pdfDoc.toString()}`;

    // 检查PDF文档级别的缓存
    const cachedResource = this.resourceCache.get(resourceKey);
    if (cachedResource?.font && this.isResourceValid(cachedResource)) {
      console.log(`✅ Font cache hit: ${family} ${weight}`);
      this.updateCacheHitRate(true);
      return cachedResource.font;
    }

    this.updateCacheHitRate(false);

    try {
      const startTime = performance.now();
      
      // 获取字体数据
      let fontData = this.fontCache.get(cacheKey);
      
      if (!fontData) {
        // 如果缓存中没有，尝试加载
        fontData = await this.loadLocalFont(family, weight);
        if (!fontData) {
          fontData = await this.loadGoogleFont(family, weight);
        }
        
        if (fontData) {
          this.fontCache.set(cacheKey, fontData);
        }
      }

      if (!fontData) {
        console.warn(`❌ Failed to load font: ${family} ${weight}`);
        return null;
      }

      // 嵌入字体到PDF
      const font = await pdfDoc.embedFont(fontData, {
        subset: true, // 使用子集减小文件大小
        customName: `${family.replace(/\s+/g, '')}-${weight}`
      });

      // 缓存到资源缓存
      this.resourceCache.set(resourceKey, {
        font,
        timestamp: Date.now(),
        size: fontData.byteLength
      });

      const loadTime = performance.now() - startTime;
      console.log(`📁 Font embedded: ${family} ${weight} (${Math.round(loadTime)}ms)`);
      
      return font;
    } catch (error) {
      console.error(`❌ Error embedding font: ${family} ${weight}`, error);
      return null;
    }
  }

  /**
   * 获取优化的图片
   */
  async getOptimizedImage(pdfDoc: PDFDocument, imageUrl: string): Promise<PDFImage | null> {
    const cacheKey = `image-${imageUrl}`;
    const resourceKey = `pdf-image-${imageUrl}-${pdfDoc.toString()}`;

    // 检查PDF文档级别的缓存
    const cachedResource = this.resourceCache.get(resourceKey);
    if (cachedResource?.image && this.isResourceValid(cachedResource)) {
      console.log(`✅ Image cache hit: ${imageUrl}`);
      this.updateCacheHitRate(true);
      return cachedResource.image;
    }

    this.updateCacheHitRate(false);

    try {
      const startTime = performance.now();
      
      // 获取图片数据
      let imageData = this.imageCache.get(cacheKey);
      
      if (!imageData) {
        // 处理相对路径
        let fullUrl = imageUrl;
        if (imageUrl.startsWith('/')) {
          fullUrl = `${window.location.origin}${imageUrl}`;
        }

        const response = await fetch(fullUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status}`);
        }

        imageData = await response.arrayBuffer();
        this.imageCache.set(cacheKey, imageData);
      }

      // 嵌入图片到PDF
      let image: PDFImage;
      if (imageUrl.toLowerCase().endsWith('.png')) {
        image = await pdfDoc.embedPng(imageData);
      } else if (imageUrl.toLowerCase().endsWith('.jpg') || imageUrl.toLowerCase().endsWith('.jpeg')) {
        image = await pdfDoc.embedJpg(imageData);
      } else {
        throw new Error(`Unsupported image format: ${imageUrl}`);
      }

      // 缓存到资源缓存
      this.resourceCache.set(resourceKey, {
        image,
        timestamp: Date.now(),
        size: imageData.byteLength
      });

      const loadTime = performance.now() - startTime;
      console.log(`🖼️ Image embedded: ${imageUrl} (${Math.round(loadTime)}ms)`);
      
      return image;
    } catch (error) {
      console.error(`❌ Error embedding image: ${imageUrl}`, error);
      return null;
    }
  }

  /**
   * 加载本地字体
   */
  private async loadLocalFont(family: string, weight: number): Promise<ArrayBuffer | undefined> {
    const fontPaths: Record<string, Record<number, string>> = {
      'Dancing Script': {
        400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
        500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
        600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
        700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
      },
      'Inter': {
        400: '/fonts/open-sans/open-sans-400.ttf',
        700: '/fonts/open-sans/open-sans-700.ttf'
      },
      'Roboto': {
        400: '/fonts/roboto/roboto-400.ttf',
        700: '/fonts/roboto/roboto-700.ttf'
      }
    };

    const fontPath = fontPaths[family]?.[weight];
    if (!fontPath) {
      return undefined;
    }

    try {
      const response = await fetch(fontPath);
      if (!response.ok) {
        return undefined;
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.warn(`Failed to load local font: ${family} ${weight}`, error);
      return undefined;
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();

    // 清理资源缓存
    Array.from(this.resourceCache.entries()).forEach(([key, resource]) => {
      if (now - resource.timestamp > this.maxAge) {
        this.resourceCache.delete(key);
      }
    });

    // 清理字体缓存
    if (this.fontCache.size > 50) {
      const keys = Array.from(this.fontCache.keys());
      for (let i = 0; i < Math.min(10, keys.length); i++) {
        this.fontCache.delete(keys[i]);
      }
    }

    // 清理图片缓存
    if (this.imageCache.size > 50) {
      const keys = Array.from(this.imageCache.keys());
      for (let i = 0; i < Math.min(10, keys.length); i++) {
        this.imageCache.delete(keys[i]);
      }
    }
  }

  /**
   * 检查资源是否有效
   */
  private isResourceValid(resource: OptimizedResource): boolean {
    const now = Date.now();
    return now - resource.timestamp < this.maxAge;
  }

  /**
   * 更新缓存命中率
   */
  private updateCacheHitRate(hit: boolean): void {
    // 简单的缓存命中率计算
    if (hit) {
      this.metrics.cacheHitRate = Math.min(this.metrics.cacheHitRate + 0.01, 1.0);
    } else {
      this.metrics.cacheHitRate = Math.max(this.metrics.cacheHitRate - 0.01, 0.0);
    }
  }

  /**
   * 加载Google字体
   */
  private async loadGoogleFont(family: string, weight: number): Promise<ArrayBuffer | undefined> {
    try {
      const url = `https://fonts.googleapis.com/css2?family=${family.replace(' ', '+')}:wght@${weight}&display=swap`;
      const response = await fetch(url);
      if (!response.ok) {
        return undefined;
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.warn(`Failed to load Google font: ${family} ${weight}`, error);
      return undefined;
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = {
      fontLoadTime: 0,
      imageLoadTime: 0,
      pdfGenerationTime: 0,
      totalTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0
    };
  }
}
