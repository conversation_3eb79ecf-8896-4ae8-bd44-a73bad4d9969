import { PDFFont } from 'pdf-lib';

/**
 * PDF文本处理工具类
 * 提供智能文本换行、边界检测和文本布局功能
 */
export class PDFTextUtils {
  /**
   * 智能文本换行处理
   * @param text 要换行的文本
   * @param font PDF字体对象
   * @param fontSize 字体大小
   * @param maxWidth 最大宽度
   * @param maxHeight 最大高度（可选）
   * @param lineHeight 行高倍数（默认1.5）
   * @returns 换行后的文本数组
   */
  static wrapText(
    text: string, 
    font: PDFFont, 
    fontSize: number, 
    maxWidth: number, 
    maxHeight?: number,
    lineHeight: number = 1.5
  ): string[] {
    if (!text || !text.trim()) return [];
    
    const actualLineHeight = fontSize * lineHeight;
    const maxLines = maxHeight ? Math.floor(maxHeight / actualLineHeight) : Infinity;
    
    // 首先按现有换行符分割
    const paragraphs = text.split(/\r?\n/);
    const allLines: string[] = [];
    
    for (const paragraph of paragraphs) {
      if (!paragraph.trim()) {
        allLines.push(''); // 保留空行
        continue;
      }
      
      const paragraphLines = this.wrapParagraph(paragraph.trim(), font, fontSize, maxWidth);
      allLines.push(...paragraphLines);
      
      // 检查是否超出最大行数限制
      if (allLines.length >= maxLines) {
        break;
      }
    }
    
    // 如果超出最大行数，截断并添加省略号
    if (allLines.length > maxLines) {
      return this.truncateWithEllipsis(allLines, maxLines, font, fontSize, maxWidth);
    }
    
    return allLines;
  }

  /**
   * 包装单个段落
   */
  private static wrapParagraph(paragraph: string, font: PDFFont, fontSize: number, maxWidth: number): string[] {
    const words = paragraph.split(/\s+/);
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      // 检查单个单词是否过长（安全处理中文字符）
      let wordWidth: number;
      try {
        wordWidth = font.widthOfTextAtSize(word, fontSize);
      } catch (error) {
        // 如果计算文本宽度失败（通常是中文字符编码问题），使用估算宽度
        console.warn(`⚠️ Failed to calculate word width for "${word}", using estimated width`);
        wordWidth = word.length * fontSize * 0.6; // 估算宽度
      }

      if (wordWidth > maxWidth) {
        // 如果当前行有内容，先保存
        if (currentLine) {
          lines.push(currentLine);
          currentLine = '';
        }

        // 强制分割长单词
        const brokenWord = this.breakLongWord(word, font, fontSize, maxWidth);
        lines.push(...brokenWord);
        continue;
      }

      const testLine = currentLine ? `${currentLine} ${word}` : word;
      let testWidth: number;
      try {
        testWidth = font.widthOfTextAtSize(testLine, fontSize);
      } catch (error) {
        // 如果计算文本宽度失败，使用估算宽度
        console.warn(`⚠️ Failed to calculate line width for "${testLine}", using estimated width`);
        testWidth = testLine.length * fontSize * 0.6; // 估算宽度
      }

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // 这种情况理论上不应该发生，因为我们已经检查了单词长度
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * 分割过长的单词
   */
  private static breakLongWord(word: string, font: PDFFont, fontSize: number, maxWidth: number): string[] {
    const lines: string[] = [];
    let currentPart = '';
    
    for (let i = 0; i < word.length; i++) {
      const char = word[i];
      const testPart = currentPart + char;
      let testWidth: number;
      try {
        testWidth = font.widthOfTextAtSize(testPart, fontSize);
      } catch (error) {
        // 如果计算文本宽度失败，使用估算宽度
        console.warn(`⚠️ Failed to calculate part width for "${testPart}", using estimated width`);
        testWidth = testPart.length * fontSize * 0.6; // 估算宽度
      }

      if (testWidth <= maxWidth) {
        currentPart = testPart;
      } else {
        if (currentPart) {
          lines.push(currentPart);
          currentPart = char;
        } else {
          // 即使单个字符也超宽，强制添加
          lines.push(char);
        }
      }
    }
    
    if (currentPart) {
      lines.push(currentPart);
    }
    
    return lines;
  }

  /**
   * 截断文本并添加省略号
   */
  private static truncateWithEllipsis(
    lines: string[], 
    maxLines: number, 
    font: PDFFont, 
    fontSize: number, 
    maxWidth: number
  ): string[] {
    const truncatedLines = lines.slice(0, maxLines);
    if (truncatedLines.length === 0) return [];
    
    const ellipsis = '...';
    const lastLine = truncatedLines[truncatedLines.length - 1];
    
    // 尝试在最后一行添加省略号
    if (font.widthOfTextAtSize(lastLine + ellipsis, fontSize) <= maxWidth) {
      truncatedLines[truncatedLines.length - 1] = lastLine + ellipsis;
    } else {
      // 如果加不下，截断最后一行的内容
      const words = lastLine.split(' ');
      let truncatedLine = '';
      
      for (let i = 0; i < words.length; i++) {
        const testLine = truncatedLine ? `${truncatedLine} ${words[i]}` : words[i];
        if (font.widthOfTextAtSize(testLine + ellipsis, fontSize) <= maxWidth) {
          truncatedLine = testLine;
        } else {
          break;
        }
      }
      
      truncatedLines[truncatedLines.length - 1] = truncatedLine + ellipsis;
    }
    
    return truncatedLines;
  }

  /**
   * 计算文本的实际尺寸
   */
  static measureText(
    text: string, 
    font: PDFFont, 
    fontSize: number, 
    lineHeight: number = 1.5
  ): { width: number; height: number } {
    const lines = text.split(/\r?\n/);
    let maxWidth = 0;
    
    for (const line of lines) {
      const lineWidth = font.widthOfTextAtSize(line, fontSize);
      maxWidth = Math.max(maxWidth, lineWidth);
    }
    
    const height = lines.length * fontSize * lineHeight;
    
    return { width: maxWidth, height };
  }

  /**
   * 检查文本是否适合指定区域
   */
  static doesTextFit(
    text: string, 
    font: PDFFont, 
    fontSize: number, 
    maxWidth: number, 
    maxHeight: number,
    lineHeight: number = 1.5
  ): boolean {
    const lines = this.wrapText(text, font, fontSize, maxWidth, maxHeight, lineHeight);
    const actualHeight = lines.length * fontSize * lineHeight;
    return actualHeight <= maxHeight;
  }

  /**
   * 自动调整字体大小以适应区域
   */
  static autoFitText(
    text: string, 
    font: PDFFont, 
    initialFontSize: number, 
    maxWidth: number, 
    maxHeight: number,
    minFontSize: number = 8,
    lineHeight: number = 1.5
  ): { fontSize: number; lines: string[] } {
    let fontSize = initialFontSize;
    let lines: string[] = [];
    
    while (fontSize >= minFontSize) {
      lines = this.wrapText(text, font, fontSize, maxWidth, maxHeight, lineHeight);
      const actualHeight = lines.length * fontSize * lineHeight;
      
      if (actualHeight <= maxHeight) {
        break;
      }
      
      fontSize -= 1;
    }
    
    return { fontSize, lines };
  }

  /**
   * 计算文本对齐位置
   */
  static calculateAlignment(
    text: string,
    font: PDFFont,
    fontSize: number,
    x: number,
    width: number,
    align: 'left' | 'center' | 'right'
  ): number {
    if (align === 'left') {
      return x;
    }
    
    const textWidth = font.widthOfTextAtSize(text, fontSize);
    
    if (align === 'center') {
      return x + (width - textWidth) / 2;
    } else if (align === 'right') {
      return x + width - textWidth;
    }
    
    return x;
  }

  /**
   * 确保坐标在页面边界内
   */
  static clampToPageBounds(
    x: number,
    y: number,
    textWidth: number,
    textHeight: number,
    pageWidth: number,
    pageHeight: number,
    margin: number = 20
  ): { x: number; y: number } {
    const clampedX = Math.max(margin, Math.min(x, pageWidth - textWidth - margin));
    const clampedY = Math.max(margin, Math.min(y, pageHeight - textHeight - margin));
    
    return { x: clampedX, y: clampedY };
  }
}
