/**
 * Batch generation performance monitoring tool
 * Used to monitor and optimize batch PDF generation performance
 */
export class BatchPerformanceMonitor {
  private static metrics = new Map<string, any>();
  private static readonly MAX_METRICS_HISTORY = 100;

  /**
   * Start monitoring task
   */
  static startTask(taskId: string, totalItems: number): void {
    const startTime = Date.now();
    
    this.metrics.set(taskId, {
      taskId,
      totalItems,
      startTime,
      endTime: null,
      completedItems: 0,
      failedItems: 0,
      memoryUsage: this.getMemoryUsage(),
      processingTimes: [],
      errors: [],
      status: 'running'
    });

    console.log(`📊 Performance monitoring started for task: ${taskId}`);
  }

  /**
   * Record single item processing completion
   */
  static recordItemCompleted(taskId: string, processingTime: number): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.completedItems++;
    metrics.processingTimes.push(processingTime);
    
    // Keep only the latest 100 processing time records
    if (metrics.processingTimes.length > this.MAX_METRICS_HISTORY) {
      metrics.processingTimes = metrics.processingTimes.slice(-this.MAX_METRICS_HISTORY);
    }

    // Update memory usage
    metrics.memoryUsage = this.getMemoryUsage();

    this.metrics.set(taskId, metrics);
  }

  /**
   * Record item processing failure
   */
  static recordItemFailed(taskId: string, error: string): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.failedItems++;
    metrics.errors.push({
      timestamp: Date.now(),
      error
    });

    // Keep only recent error records
    if (metrics.errors.length > 50) {
      metrics.errors = metrics.errors.slice(-50);
    }

    this.metrics.set(taskId, metrics);
  }

  /**
   * Complete task monitoring
   */
  static endTask(taskId: string): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.endTime = Date.now();
    metrics.status = 'completed';
    
    const summary = this.generateSummary(taskId);
    console.log(`📊 Performance monitoring completed for task: ${taskId}`, summary);

    this.metrics.set(taskId, metrics);
  }

  /**
   * Get task performance summary
   */
  static generateSummary(taskId: string): any {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return null;

    const totalTime = (metrics.endTime || Date.now()) - metrics.startTime;
    const avgProcessingTime = metrics.processingTimes.length > 0 
      ? metrics.processingTimes.reduce((a: number, b: number) => a + b, 0) / metrics.processingTimes.length
      : 0;

    const throughput = metrics.completedItems > 0 
      ? (metrics.completedItems / (totalTime / 1000)) * 60 // items per minute
      : 0;

    return {
      taskId,
      totalItems: metrics.totalItems,
      completedItems: metrics.completedItems,
      failedItems: metrics.failedItems,
      successRate: metrics.totalItems > 0 ? (metrics.completedItems / metrics.totalItems) * 100 : 0,
      totalTime: totalTime,
      avgProcessingTime: Math.round(avgProcessingTime),
      throughput: Math.round(throughput * 100) / 100,
      memoryUsage: metrics.memoryUsage,
      errorCount: metrics.errors.length,
      status: metrics.status
    };
  }

  /**
   * Get real-time performance metrics
   */
  static getRealTimeMetrics(taskId: string): any {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return null;

    const currentTime = Date.now();
    const elapsedTime = currentTime - metrics.startTime;
    const recentProcessingTimes = metrics.processingTimes.slice(-10); // Latest 10
    
    const avgRecentProcessingTime = recentProcessingTimes.length > 0
      ? recentProcessingTimes.reduce((a: number, b: number) => a + b, 0) / recentProcessingTimes.length
      : 0;

    const estimatedTimeRemaining = avgRecentProcessingTime > 0 && metrics.completedItems > 0
      ? ((metrics.totalItems - metrics.completedItems) * avgRecentProcessingTime) / 1000
      : null;

    const currentThroughput = metrics.completedItems > 0 && elapsedTime > 0
      ? (metrics.completedItems / (elapsedTime / 1000)) * 60
      : 0;

    return {
      taskId,
      elapsedTime,
      completedItems: metrics.completedItems,
      failedItems: metrics.failedItems,
      remainingItems: metrics.totalItems - metrics.completedItems - metrics.failedItems,
      progress: Math.round(((metrics.completedItems + metrics.failedItems) / metrics.totalItems) * 100),
      avgRecentProcessingTime: Math.round(avgRecentProcessingTime),
      estimatedTimeRemaining: estimatedTimeRemaining ? Math.round(estimatedTimeRemaining) : null,
      currentThroughput: Math.round(currentThroughput * 100) / 100,
      memoryUsage: this.getMemoryUsage(),
      recentErrors: metrics.errors.slice(-5) // Latest 5 errors
    };
  }

  /**
   * Get performance recommendations
   */
  static getPerformanceRecommendations(taskId: string): string[] {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return [];

    const recommendations: string[] = [];
    const summary = this.generateSummary(taskId);

    // Recommendations based on success rate
    if (summary.successRate < 90) {
      recommendations.push('Low success rate, recommend checking data format and network connection');
    }

    // Recommendations based on processing speed - optimized thresholds
    if (summary.avgProcessingTime > 3000) { // Reduced to 3 seconds
      recommendations.push('Processing speed is slow, recommend increasing concurrency or checking server performance');
    } else if (summary.avgProcessingTime < 500) { // Less than 500ms
      recommendations.push('Processing speed is good, consider increasing concurrency to improve throughput');
    }

    // Recommendations based on memory usage
    if (summary.memoryUsage && summary.memoryUsage.usedJSHeapSize > 100 * 1024 * 1024) { // Exceeds 100MB
      recommendations.push('High memory usage, recommend reducing batch size');
    }

    // Recommendations based on error rate
    const errorRate = (summary.errorCount / summary.totalItems) * 100;
    if (errorRate > 10) {
      recommendations.push('High error rate, recommend checking data quality');
    }

    // Recommendations based on throughput
    const realTimeMetrics = this.getRealTimeMetrics(taskId);
    if (realTimeMetrics && realTimeMetrics.currentThroughput < 10) { // Less than 10 per minute
      recommendations.push('Low throughput, recommend checking network connection and server performance');
    }

    // Recommendations based on throughput
    if (summary.throughput < 10) { // Less than 10 per minute
      recommendations.push('Low processing efficiency, recommend optimizing network environment or reducing data complexity');
    }

    return recommendations;
  }

  /**
   * Clean up expired monitoring data
   */
  static cleanup(): void {
    const now = Date.now();
    const expiredTasks: string[] = [];

    this.metrics.forEach((metrics, taskId) => {
      const age = now - metrics.startTime;
      // Clean up data from 24 hours ago
      if (age > 24 * 60 * 60 * 1000) {
        expiredTasks.push(taskId);
      }
    });

    expiredTasks.forEach(taskId => {
      this.metrics.delete(taskId);
    });

    if (expiredTasks.length > 0) {
      console.log(`🧹 Cleaned up ${expiredTasks.length} expired performance metrics`);
    }
  }

  /**
   * Get memory usage
   */
  private static getMemoryUsage(): any {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * Get performance overview of all active tasks
   */
  static getOverview(): any {
    const activeTasks = Array.from(this.metrics.values()).filter(m => m.status === 'running');
    const completedTasks = Array.from(this.metrics.values()).filter(m => m.status === 'completed');

    return {
      activeTasks: activeTasks.length,
      completedTasks: completedTasks.length,
      totalItemsProcessed: completedTasks.reduce((sum, m) => sum + m.completedItems, 0),
      avgSuccessRate: completedTasks.length > 0 
        ? completedTasks.reduce((sum, m) => sum + (m.completedItems / m.totalItems), 0) / completedTasks.length * 100
        : 0,
      avgThroughput: completedTasks.length > 0
        ? completedTasks.reduce((sum, m) => {
            const totalTime = m.endTime - m.startTime;
            return sum + (m.completedItems / (totalTime / 1000)) * 60;
          }, 0) / completedTasks.length
        : 0
    };
  }
}
