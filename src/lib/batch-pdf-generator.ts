import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import {
  BatchCertificateData,
  BatchGenerationError,
  CertificateTemplate,
  CertificateData
} from '@/types/certificate';
// 移除未使用的导入
import { BatchTaskManager } from '@/lib/batch-task-manager';
import { BatchPerformanceMonitor } from '@/lib/batch-performance-monitor';
import { PDFTextUtils } from '@/lib/pdf-text-utils';
import path from 'path';
import fs from 'fs/promises';

/**
 * 批量PDF生成器
 * 负责批量生成证书PDF文件
 */
export class BatchPDFGenerator {
  private taskId: string;
  private template: CertificateTemplate;
  private certificates: BatchCertificateData[];
  private outputDir: string;
  private maxConcurrency: number;

  constructor(
    taskId: string,
    template: CertificateTemplate,
    certificates: BatchCertificateData[],
    options: {
      outputDir?: string;
      maxConcurrency?: number;
    } = {}
  ) {
    this.taskId = taskId;
    this.template = template;
    this.certificates = certificates;
    this.outputDir = options.outputDir || this.getDefaultOutputDir();
    this.maxConcurrency = options.maxConcurrency || 8; // 提高默认并发数
  }

  /**
   * Start batch generation with timeout and resource management
   */
  async generate(): Promise<{
    success: boolean;
    generatedFiles: string[];
    errors: BatchGenerationError[];
  }> {
    console.log(`🚀 Starting batch PDF generation for task: ${this.taskId}`);
    console.log(`📊 Total certificates: ${this.certificates.length}`);
    console.log(`🎯 Template: ${this.template.displayName}`);

    // Set up timeout for the entire operation
    const timeoutMs = 30 * 60 * 1000; // 30 minutes timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Batch generation timed out after ${timeoutMs / 1000} seconds`));
      }, timeoutMs);
    });

    try {
      return await Promise.race([
        this.executeGeneration(),
        timeoutPromise
      ]);
    } catch (error) {
      console.error(`❌ Batch generation failed for task ${this.taskId}:`, error);
      // Ensure cleanup on failure
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Execute the actual generation process
   */
  private async executeGeneration(): Promise<{
    success: boolean;
    generatedFiles: string[];
    errors: BatchGenerationError[];
  }> {
    // Ensure output directory exists
    await this.ensureOutputDirectory();

    // Start performance monitoring
    BatchPerformanceMonitor.startTask(this.taskId, this.certificates.length);

    // Update task status to processing
    BatchTaskManager.updateTaskStatus(this.taskId, 'processing' as any);

    const generatedFiles: string[] = [];
    const errors: BatchGenerationError[] = [];
    let completedCount = 0;

    try {
      // Process certificates in controlled batches
      const batches = this.createBatches(this.certificates, this.maxConcurrency);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`📦 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} items)`);

        // Process current batch with individual timeouts
        const batchPromises = batch.map(async (certificate) => {
          const itemStartTime = Date.now();
          const itemTimeoutMs = 60000; // 1 minute per certificate

          try {
            // Add timeout for individual certificate generation
            const generatePromise = this.generateSinglePDF(certificate);
            const itemTimeoutPromise = new Promise<never>((_, reject) => {
              setTimeout(() => {
                reject(new Error(`Certificate generation timed out for ${certificate.recipientName}`));
              }, itemTimeoutMs);
            });

            const filename = await Promise.race([generatePromise, itemTimeoutPromise]);
            generatedFiles.push(filename);
            completedCount++;

            // Record performance metrics
            const processingTime = Date.now() - itemStartTime;
            BatchPerformanceMonitor.recordItemCompleted(this.taskId, processingTime);

            // Update progress
            BatchTaskManager.updateTaskProgress(this.taskId, completedCount, errors.length);

            console.log(`✅ Generated PDF ${completedCount}/${this.certificates.length}: ${filename} (${processingTime}ms)`);

          } catch (error) {
            const batchError: BatchGenerationError = {
              rowIndex: certificate.rowIndex || 0,
              message: error instanceof Error ? error.message : 'PDF generation failed',
              certificateData: certificate
            };

            errors.push(batchError);
            completedCount++; // 增加完成计数，即使失败也算作已处理
            BatchTaskManager.addTaskError(this.taskId, batchError);

            // Record performance metrics
            BatchPerformanceMonitor.recordItemFailed(this.taskId, batchError.message);

            // Update progress even for failed items
            BatchTaskManager.updateTaskProgress(this.taskId, completedCount - errors.length, errors.length);

            console.error(`❌ Failed to generate PDF for certificate ${certificate.rowIndex}:`, error);
          }
        });

        // Wait for current batch to complete with timeout protection
        try {
          await Promise.all(batchPromises);
        } catch (error) {
          console.error(`❌ Batch ${batchIndex + 1} failed:`, error);
          // Continue with next batch even if current batch has failures
        }

        // Add small delay between batches to prevent resource exhaustion
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Ensure final progress update
      BatchTaskManager.updateTaskProgress(this.taskId, completedCount - errors.length, errors.length);

      // Complete performance monitoring
      BatchPerformanceMonitor.endTask(this.taskId);

      // Get performance summary and recommendations
      const performanceSummary = BatchPerformanceMonitor.generateSummary(this.taskId);
      const recommendations = BatchPerformanceMonitor.getPerformanceRecommendations(this.taskId);

      // Update final status
      const success = errors.length === 0;
      BatchTaskManager.completeTask(this.taskId, success);

      console.log(`🎉 Batch generation completed. Success: ${generatedFiles.length}, Errors: ${errors.length}`);
      console.log(`📊 Performance Summary:`, performanceSummary);

      if (recommendations.length > 0) {
        console.log(`💡 Performance Recommendations:`, recommendations);
      }

      return {
        success,
        generatedFiles,
        errors
      };

    } catch (error) {
      console.error('❌ Batch generation failed:', error);
      BatchTaskManager.completeTask(this.taskId, false);

      // Cleanup resources
      await this.cleanup();

      throw error;
    }
  }



  /**
   * 生成单个PDF - 使用与单证书生成相同的服务器端逻辑
   */
  private async generateSinglePDF(certificate: BatchCertificateData): Promise<string> {
    try {
      // 转换为标准证书数据格式
      const certificateData: CertificateData = {
        templateId: this.template.id,
        recipientName: certificate.recipientName,
        date: certificate.date,
        signature: certificate.signature,
        details: certificate.details
      };

      // 使用与单证书生成相同的服务器端PDF生成逻辑
      const pdfBytes = await this.generatePDFUsingServerLogic(certificateData);

      // 生成文件名
      const filename = this.generateFilename(certificate);
      const filepath = path.join(this.outputDir, filename);

      // 保存文件
      await fs.writeFile(filepath, pdfBytes);

      return filename;

    } catch (error) {
      console.error(`Failed to generate PDF for certificate:`, error);
      throw new Error(`PDF生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 使用服务器端逻辑生成PDF（与单证书生成保持一致）
   */
  private async generatePDFUsingServerLogic(data: CertificateData): Promise<Uint8Array> {
    // 创建PDF文档
    const pdfDoc = await PDFDocument.create();
    pdfDoc.registerFontkit(fontkit);

    // 初始化字体嵌入器
    const fontEmbedder = new ServerFontEmbedder(pdfDoc);

    // 创建页面
    const [width, height] = this.template.orientation === 'landscape' ? [842, 595] : [595, 842];
    const page = pdfDoc.addPage([width, height]);

    // 绘制背景 - 与单个生成保持一致的逻辑
    await this.drawBackgroundForBatch(page, pdfDoc, width, height);

    // 绘制文本元素
    await this.drawTextForBatch(page, fontEmbedder, 'name', data.recipientName, width, height);
    await this.drawTextForBatch(page, fontEmbedder, 'details', data.details, width, height);
    await this.drawTextForBatch(page, fontEmbedder, 'date', data.date, width, height);
    await this.drawTextForBatch(page, fontEmbedder, 'signature', data.signature, width, height);

    // 保存PDF
    return await pdfDoc.save();
  }

  /**
   * 生成文件名
   */
  private generateFilename(certificate: BatchCertificateData): string {
    // 清理文件名中的特殊字符
    const safeName = certificate.recipientName
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
      .substring(0, 50); // 限制长度
    
    const timestamp = Date.now();
    const rowSuffix = certificate.rowIndex ? `_row${certificate.rowIndex}` : '';
    
    return `certificate_${safeName}${rowSuffix}_${timestamp}.pdf`;
  }

  /**
   * 创建批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 确保输出目录存在
   */
  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.access(this.outputDir);
    } catch {
      await fs.mkdir(this.outputDir, { recursive: true });
      console.log(`📁 Created output directory: ${this.outputDir}`);
    }
  }

  /**
   * 获取默认输出目录
   */
  private getDefaultOutputDir(): string {
    return path.join(process.cwd(), 'temp', 'batch-pdfs', this.taskId);
  }

  /**
   * Cleanup resources and temporary files
   */
  async cleanup(): Promise<void> {
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Clean up temporary files (only if generation failed)
      // Successful generations keep files for download
      const task = BatchTaskManager.getTask(this.taskId);
      if (task && task.status === 'failed') {
        await fs.rm(this.outputDir, { recursive: true, force: true });
        console.log(`🧹 Cleaned up temporary files for failed task: ${this.taskId}`);
      }

      console.log(`🧹 Resource cleanup completed for task ${this.taskId}`);
    } catch (error) {
      console.error(`Failed to cleanup resources for task ${this.taskId}:`, error);
    }
  }

  /**
   * 获取生成的文件列表
   */
  async getGeneratedFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.outputDir);
      return files.filter(file => file.endsWith('.pdf'));
    } catch {
      return [];
    }
  }

  /**
   * 获取输出目录路径
   */
  getOutputDirectory(): string {
    return this.outputDir;
  }

  /**
   * 绘制背景（批量生成专用）- 与单个生成保持一致
   */
  private async drawBackgroundForBatch(page: any, pdfDoc: PDFDocument, width: number, height: number) {
    // 如果有背景图片，优先绘制背景图片
    if (this.template.backgroundImage) {
      try {
        await this.drawBackgroundImageForBatch(page, pdfDoc, width, height);
        console.log('✅ Background image drawn, skipping color background');
        return; // 有背景图片时不绘制颜色背景
      } catch (error) {
        console.warn('Failed to load background image, falling back to color background:', error);
        // 如果背景图片加载失败，继续绘制颜色背景
      }
    }

    // 绘制颜色背景（仅在没有背景图片或背景图片加载失败时）
    if (this.template.style?.background) {
      const bgColor = this.hexToRgb(this.template.style.background);
      page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: rgb(bgColor.r, bgColor.g, bgColor.b)
      });
      console.log(`✅ Color background drawn: ${this.template.style.background}`);
    }
  }

  /**
   * 绘制背景图片（批量生成专用）
   */
  private async drawBackgroundImageForBatch(page: any, pdfDoc: PDFDocument, width: number, height: number) {
    if (!this.template.backgroundImage) return;

    try {
      const imagePath = this.template.backgroundImage;

      // 如果是相对路径，转换为文件系统路径
      if (imagePath.startsWith('/')) {
        const publicPath = path.join(process.cwd(), 'public', imagePath);
        console.log(`Loading background image from: ${publicPath}`);

        // Add timeout protection for file reading
        const imageBytes = await Promise.race([
          fs.readFile(publicPath),
          new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('Image loading timeout')), 10000); // 10 second timeout
          })
        ]);

        // 根据文件扩展名确定图片类型
        let image;
        if (imagePath.toLowerCase().endsWith('.png')) {
          image = await pdfDoc.embedPng(imageBytes);
        } else if (imagePath.toLowerCase().endsWith('.jpg') || imagePath.toLowerCase().endsWith('.jpeg')) {
          image = await pdfDoc.embedJpg(imageBytes);
        } else {
          throw new Error(`Unsupported image format: ${imagePath}`);
        }

        // 获取图片的原始尺寸
        const imageDims = image.scale(1);

        // 计算缩放比例以适应页面
        const scaleX = width / imageDims.width;
        const scaleY = height / imageDims.height;
        const scale = Math.min(scaleX, scaleY);

        // 计算居中位置
        const scaledWidth = imageDims.width * scale;
        const scaledHeight = imageDims.height * scale;
        const x = (width - scaledWidth) / 2;
        const y = (height - scaledHeight) / 2;

        // 绘制图片，保持宽高比并居中
        page.drawImage(image, {
          x,
          y,
          width: scaledWidth,
          height: scaledHeight,
        });

        console.log(`✅ Background image drawn successfully: ${imagePath}`);
      } else {
        throw new Error('Only relative paths starting with "/" are supported');
      }
    } catch (error) {
      console.error(`❌ Error drawing background image:`, error);
      throw error;
    }
  }

  /**
   * 绘制文本（批量生成专用）- 修复样式配置一致性
   */
  private async drawTextForBatch(
    page: any,
    fontEmbedder: ServerFontEmbedder,
    type: 'name' | 'details' | 'date' | 'signature',
    text: string,
    width: number,
    height: number
  ) {
    const layout = this.template.layout[type];
    if (!text || !layout) return;

    // 使用layout中的字体配置，确保与单证书生成一致
    const fontFamily = layout.fontFamily;
    const fontWeight = layout.fontWeight || 400;
    const fontSize = layout.fontSize;
    const color = layout.color;

    console.log(`🎨 Drawing ${type} with layout config: font=${fontFamily} ${fontWeight}, size=${fontSize}, color=${color}`);

    // 获取字体
    const font = await fontEmbedder.embedFont(fontFamily, fontWeight);

    // 计算位置 - 使用layout中的坐标
    const x = layout.x;
    const y = height - layout.y;

    // 特殊处理details字段，使用智能文本换行
    if (type === 'details') {
      await this.drawDetailsTextForBatch(page, text, font, {
        family: fontFamily,
        size: fontSize,
        color: color,
        weight: fontWeight
      }, layout, width, height);
      return;
    }

    // 计算对齐位置（安全处理中文字符）
    let finalX = x;
    if (layout.align === 'center') {
      try {
        const textWidth = font.widthOfTextAtSize(text, fontSize);
        finalX = x - textWidth / 2;
      } catch (error) {
        // 如果计算文本宽度失败（通常是中文字符编码问题），使用估算宽度
        console.warn(`⚠️ Failed to calculate text width for "${text}", using estimated width`);
        const estimatedWidth = text.length * fontSize * 0.6; // 估算宽度
        finalX = x - estimatedWidth / 2;
      }
    } else if (layout.align === 'right') {
      try {
        const textWidth = font.widthOfTextAtSize(text, fontSize);
        finalX = x - textWidth;
      } catch (error) {
        // 如果计算文本宽度失败，使用估算宽度
        console.warn(`⚠️ Failed to calculate text width for "${text}", using estimated width`);
        const estimatedWidth = text.length * fontSize * 0.6; // 估算宽度
        finalX = x - estimatedWidth;
      }
    }

    // 其他字段直接绘制
    page.drawText(text, {
      x: finalX,
      y,
      size: fontSize,
      font,
      color: (() => {
        const colorObj = this.hexToRgb(color);
        return rgb(colorObj.r, colorObj.g, colorObj.b);
      })()
    });

    console.log(`✅ Drew ${type}: "${text}" at (${finalX}, ${y}) with font ${fontFamily} ${fontWeight}`);
  }

  /**
   * 绘制详细信息文本（批量生成专用）- 修复样式配置一致性
   */
  private async drawDetailsTextForBatch(page: any, text: string, font: any, fontConfig: any, layout: any, _pageWidth: number, pageHeight: number) {
    const fontSize = fontConfig.size;
    const maxWidth = layout.width;
    const startX = layout.x;
    const startY = pageHeight - layout.y;

    console.log(`🔍 Drawing details text: "${text}"`);
    console.log(`📐 Layout: x=${layout.x}px, y=${layout.y}px, width=${layout.width}px, height=${layout.height}px`);
    console.log(`📏 Calculated: startX=${startX}, startY=${startY}, maxWidth=${maxWidth}`);

    // 计算可用高度 - 使用layout中的height配置
    const maxHeight = layout.height || 100; // 使用layout配置的高度

    // 使用智能文本换行
    const lines = PDFTextUtils.wrapText(text, font, fontSize, maxWidth, maxHeight);
    const lineHeight = fontSize * 1.5;

    console.log(`📝 Text wrapped into ${lines.length} lines, maxHeight=${maxHeight}`);

    // 绘制每一行
    lines.forEach((line, index) => {
      if (!line.trim()) return;

      const currentY = startY - (index * lineHeight);

      console.log(`📍 Line ${index + 1}: "${line}" at Y=${currentY}`);

      // 检查是否超出可用高度
      if (index * lineHeight > maxHeight) {
        console.warn(`⚠️ Skipping line ${index + 1} as it would exceed available height (${index * lineHeight} > ${maxHeight})`);
        return;
      }

      // 计算对齐位置
      let x = startX;
      if (layout.align === 'center') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX - lineWidth / 2;
      } else if (layout.align === 'right') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX - lineWidth;
      }

      // 使用计算出的位置，不进行过度的边界限制
      const finalX = x;
      const finalY = currentY;

      page.drawText(line, {
        x: finalX,
        y: finalY,
        size: fontSize,
        font,
        color: (() => {
          const color = this.hexToRgb(fontConfig.color);
          return rgb(color.r, color.g, color.b);
        })()
      });

      console.log(`✅ Drew line ${index + 1} at (${finalX}, ${finalY})`);
    });

    console.log(`✅ Drew details text: ${lines.length} lines, font: ${fontConfig.family}`);
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return { r: 0, g: 0, b: 0 };
    }
    return {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    };
  }
}

/**
 * 服务器端字体嵌入器（批量生成专用）
 */
class ServerFontEmbedder {
  private pdfDoc: PDFDocument;
  private fontCache = new Map<string, any>();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
  }

  async embedFont(family: string, weight: number): Promise<any> {
    const cacheKey = `${family}-${weight}`;

    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey);
    }

    try {
      // 字体文件路径映射 - 使用正确的子目录路径
      const fontPaths: Record<string, Record<number, string>> = {
        'Dancing Script': {
          400: path.join(process.cwd(), 'public', 'fonts', 'Dancing_Script', 'DancingScript-Regular.ttf'),
          700: path.join(process.cwd(), 'public', 'fonts', 'Dancing_Script', 'DancingScript-Bold.ttf')
        },
        'Source Sans Pro': {
          400: path.join(process.cwd(), 'public', 'fonts', 'open-sans', 'open-sans-400.ttf'),
          700: path.join(process.cwd(), 'public', 'fonts', 'open-sans', 'open-sans-700.ttf')
        },
        'Great Vibes': {
          400: path.join(process.cwd(), 'public', 'fonts', 'Lavishly_Yours', 'LavishlyYours-Regular.ttf')
        },
        'Roboto': {
          400: path.join(process.cwd(), 'public', 'fonts', 'roboto', 'roboto-400.ttf'),
          700: path.join(process.cwd(), 'public', 'fonts', 'roboto', 'roboto-700.ttf')
        }
      };

      console.log(`🔍 Looking for font: ${family} ${weight}`);
      console.log(`📁 Font path would be: ${fontPaths[family]?.[weight]}`);

      const fontPath = fontPaths[family]?.[weight];

      if (fontPath) {
        try {
          const fontBytes = await fs.readFile(fontPath);
          const font = await this.pdfDoc.embedFont(fontBytes, {
            subset: true,
            customName: `${family.replace(/\s+/g, '')}-${weight}`
          });

          this.fontCache.set(cacheKey, font);
          console.log(`✅ Font embedded: ${family} ${weight}`);
          return font;
        } catch (fileError) {
          console.warn(`⚠️ Font file not found: ${fontPath}, using fallback`);
        }
      }

      // 后备到标准字体
      console.log(`⚠️ No font file for ${family} ${weight}, using fallback`);
      const fallbackFont = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);
      const fallbackFont = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;
    }
  }
}
