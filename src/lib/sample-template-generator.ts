'use client';

import * as XLSX from 'xlsx';

/**
 * Sample Template Generator
 * Creates sample Excel/CSV files with example certificate data
 */
export class SampleTemplateGenerator {
  
  /**
   * Generate sample certificate data
   */
  private static getSampleData() {
    return [
      // Header row
      ['Recipient Name', 'Date', 'Signature', 'Certificate Details'],
      
      // Sample data rows
      [
        '<PERSON>',
        '2024-03-15',
        'Dr. <PERSON>',
        'Successfully completed the Advanced Project Management Certification Program with distinction'
      ],
      [
        '<PERSON>',
        '2024-03-15',
        'Dr. <PERSON>',
        'Demonstrated exceptional leadership skills in the Digital Marketing Masterclass'
      ],
      [
        '<PERSON>',
        '2024-03-16',
        'Prof<PERSON> <PERSON>',
        'Achieved outstanding performance in the Data Science and Analytics Course'
      ],
      [
        '<PERSON>',
        '2024-03-16',
        'Prof. <PERSON>',
        'Completed the Cybersecurity Fundamentals Training with excellent results'
      ],
      [
        '<PERSON>',
        '2024-03-17',
        'Dr. <PERSON>',
        'Successfully finished the Business Strategy and Innovation Workshop'
      ]
    ];
  }

  /**
   * Generate and download Excel template file
   */
  static downloadExcelTemplate(filename: string = 'certificate_template.xlsx') {
    try {
      const data = this.getSampleData();
      
      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.aoa_to_sheet(data);
      
      // Set column widths for better readability
      const columnWidths = [
        { wch: 20 }, // Recipient Name
        { wch: 12 }, // Date
        { wch: 20 }, // Signature
        { wch: 60 }  // Certificate Details
      ];
      worksheet['!cols'] = columnWidths;
      
      // Style the header row
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "2563EB" } }, // Blue background
        alignment: { horizontal: "center", vertical: "center" }
      };
      
      // Apply header styling
      ['A1', 'B1', 'C1', 'D1'].forEach(cell => {
        if (worksheet[cell]) {
          worksheet[cell].s = headerStyle;
        }
      });
      
      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Certificate Data');
      
      // Generate and download file
      XLSX.writeFile(workbook, filename);
      
      console.log(`✅ Excel template downloaded: ${filename}`);
      return true;
      
    } catch (error) {
      console.error('❌ Failed to generate Excel template:', error);
      return false;
    }
  }

  /**
   * Generate and download CSV template file
   */
  static downloadCSVTemplate(filename: string = 'certificate_template.csv') {
    try {
      const data = this.getSampleData();
      
      // Convert to CSV format
      const csvContent = data.map(row => 
        row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',')
      ).join('\n');
      
      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      
      console.log(`✅ CSV template downloaded: ${filename}`);
      return true;
      
    } catch (error) {
      console.error('❌ Failed to generate CSV template:', error);
      return false;
    }
  }

  /**
   * Get sample data as JSON for preview
   */
  static getSampleDataPreview() {
    const data = this.getSampleData();
    const headers = data[0];
    const rows = data.slice(1);
    
    return {
      headers,
      rows,
      totalRows: rows.length,
      description: 'Sample certificate data with proper formatting and realistic examples'
    };
  }

  /**
   * Validate if uploaded data matches expected format
   */
  static validateDataFormat(data: any[][]): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    if (!data || data.length === 0) {
      issues.push('File is empty');
      return { isValid: false, issues, suggestions };
    }
    
    const expectedColumns = 4;
    const firstRow = data[0];
    
    if (firstRow.length < expectedColumns) {
      issues.push(`Expected ${expectedColumns} columns, found ${firstRow.length}`);
      suggestions.push('Ensure your file has columns for: Recipient Name, Date, Signature, Certificate Details');
    }
    
    // Check for header row
    const hasHeaders = firstRow.every(cell => 
      typeof cell === 'string' && cell.trim().length > 0
    );
    
    if (!hasHeaders) {
      suggestions.push('Consider adding a header row for better data organization');
    }
    
    // Check data rows
    const dataRows = data.slice(1);
    if (dataRows.length === 0) {
      issues.push('No data rows found');
      suggestions.push('Add at least one row of certificate data');
    }
    
    // Check for empty cells in critical columns
    dataRows.forEach((row, index) => {
      if (!row[0] || row[0].toString().trim() === '') {
        issues.push(`Row ${index + 2}: Missing recipient name`);
      }
      if (!row[1] || row[1].toString().trim() === '') {
        issues.push(`Row ${index + 2}: Missing date`);
      }
      if (!row[2] || row[2].toString().trim() === '') {
        issues.push(`Row ${index + 2}: Missing signature`);
      }
      if (!row[3] || row[3].toString().trim() === '') {
        issues.push(`Row ${index + 2}: Missing certificate details`);
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * Get format instructions for users
   */
  static getFormatInstructions() {
    return {
      title: 'File Format Requirements',
      instructions: [
        'Use Excel (.xlsx, .xls) or CSV format',
        'Include a header row (recommended)',
        'Maximum file size: 10MB',
        'Maximum rows: 1000 certificates per batch'
      ],
      columns: [
        {
          name: 'Column A',
          field: 'Recipient Name',
          description: 'Full name of the certificate recipient',
          example: 'John Smith',
          required: true
        },
        {
          name: 'Column B',
          field: 'Date',
          description: 'Certificate issue date',
          example: '2024-03-15',
          required: true
        },
        {
          name: 'Column C',
          field: 'Signature',
          description: 'Name of the person or authority signing',
          example: 'Dr. Sarah Johnson',
          required: true
        },
        {
          name: 'Column D',
          field: 'Certificate Details',
          description: 'Description of the achievement or completion',
          example: 'Successfully completed the Advanced Project Management Certification Program',
          required: true
        }
      ]
    };
  }
}
