import { PDFDocument, PDFPage, rgb, StandardFonts } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { promises as fs } from 'fs';
import path from 'path';
import { PDFTextUtils } from './pdf-text-utils';

/**
 * PDF模板生成器 - 组件化的PDF生成系统
 * 支持基于配置的图片模板和字段参数生成对应的PDF证书
 */

export interface PDFTemplateConfig {
  id: string;
  name: string;
  orientation: 'landscape' | 'portrait';
  dimensions: {
    width: number;
    height: number;
  };
  background?: {
    color?: string;
    image?: string;
  };
  border?: {
    enabled: boolean;
    color: string;
    width: number;
    style: 'solid' | 'dashed';
  };
  fields: PDFFieldConfig[];
}

export interface PDFFieldConfig {
  id: string;
  type: 'text' | 'image';
  position: {
    x: number;
    y: number;
  };
  dimensions?: {
    width: number;
    height: number;
  };
  text?: {
    font: {
      family: string;
      weight: number;
      size: number;
    };
    color: string;
    align: 'left' | 'center' | 'right';
    maxLength?: number;
    lineHeight?: number;
  };
  image?: {
    path: string;
    fit: 'contain' | 'cover' | 'fill';
  };
}

export interface PDFGenerationData {
  [fieldId: string]: string;
}

/**
 * 字体管理器
 */
class FontManager {
  private pdfDoc: PDFDocument;
  private fontCache = new Map<string, any>();

  // 服务器端字体文件路径
  private static readonly FONT_PATHS = {
    'Dancing Script': {
      400: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Regular.ttf'),
      500: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Medium.ttf'),
      600: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-SemiBold.ttf'),
      700: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Bold.ttf')
    },
    'Open Sans': {
      400: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-400.ttf'),
      700: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-700.ttf')
    },
    'Roboto': {
      400: path.join(process.cwd(), 'public/fonts/roboto/roboto-400.ttf'),
      700: path.join(process.cwd(), 'public/fonts/roboto/roboto-700.ttf')
    },
    'Helvetica': {
      400: StandardFonts.Helvetica,
      700: StandardFonts.HelveticaBold
    },
    'Times': {
      400: StandardFonts.TimesRoman,
      700: StandardFonts.TimesRomanBold
    }
  };

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
  }

  async embedFont(family: string, weight: number = 400) {
    const cacheKey = `${family}-${weight}`;
    
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey);
    }

    try {
      const fontPath = (FontManager.FONT_PATHS as any)[family]?.[weight];
      
      if (fontPath && typeof fontPath === 'string') {
        // 自定义字体文件
        console.log(`📁 Loading font: ${fontPath}`);
        const fontBytes = await fs.readFile(fontPath);
        const font = await this.pdfDoc.embedFont(fontBytes, {
          subset: true,
          customName: `${family.replace(/\s+/g, '')}-${weight}`
        });
        
        this.fontCache.set(cacheKey, font);
        console.log(`✅ Font embedded: ${family} ${weight}`);
        return font;
      } else if (fontPath) {
        // 标准字体
        const font = await this.pdfDoc.embedStandardFont(fontPath);
        this.fontCache.set(cacheKey, font);
        return font;
      }

      // 后备字体
      console.log(`⚠️ No font file for ${family} ${weight}, using Helvetica`);
      const fallbackFont = await this.pdfDoc.embedStandardFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);
      const fallbackFont = await this.pdfDoc.embedStandardFont(StandardFonts.Helvetica);
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;
    }
  }
}

/**
 * PDF模板生成器主类
 */
export class PDFTemplateGenerator {
  private config: PDFTemplateConfig;
  private data: PDFGenerationData;
  private pdfDoc!: PDFDocument;
  private fontManager!: FontManager;

  constructor(config: PDFTemplateConfig, data: PDFGenerationData) {
    this.config = config;
    this.data = data;
  }

  /**
   * 生成PDF
   */
  async generate(): Promise<Uint8Array> {
    console.log(`🚀 Starting PDF generation for template: ${this.config.name}`);
    
    // 创建PDF文档
    this.pdfDoc = await PDFDocument.create();
    this.pdfDoc.registerFontkit(fontkit);
    
    // 初始化字体管理器
    this.fontManager = new FontManager(this.pdfDoc);
    
    // 创建页面
    const { width, height } = this.config.dimensions;
    const page = this.pdfDoc.addPage([width, height]);
    
    // 绘制背景
    await this.drawBackground(page);
    
    // 绘制边框
    await this.drawBorder(page);
    
    // 绘制字段
    await this.drawFields(page);
    
    console.log('✅ PDF generation completed');
    return await this.pdfDoc.save();
  }

  /**
   * 绘制背景
   */
  private async drawBackground(page: PDFPage) {
    const { background } = this.config;
    if (!background) return;

    const { width, height } = this.config.dimensions;

    // 先绘制颜色背景
    if (background.color) {
      const bgColor = this.hexToRgb(background.color);
      page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: bgColor
      });
    }

    // 然后绘制背景图片（如果有的话）
    if (background.image) {
      try {
        await this.drawBackgroundImage(page, background.image);
      } catch (error) {
        console.error('Error loading background image:', error);
        // 背景图片加载失败时继续使用颜色背景
      }
    }
  }

  /**
   * 绘制背景图片
   */
  private async drawBackgroundImage(page: PDFPage, imagePath: string) {
    const { width, height } = this.config.dimensions;

    try {
      // 获取图片数据
      const imageUrl = imagePath;

      // 如果是相对路径，需要转换为绝对路径
      if (imageUrl.startsWith('/')) {
        // 在服务器端，我们需要使用文件系统路径
        const publicPath = path.join(process.cwd(), 'public', imageUrl);
        console.log(`Loading background image from: ${publicPath}`);

        const imageBytes = await fs.readFile(publicPath);

        // 根据文件扩展名确定图片类型
        let image;
        if (imageUrl.toLowerCase().endsWith('.png')) {
          image = await this.pdfDoc.embedPng(imageBytes);
        } else if (imageUrl.toLowerCase().endsWith('.jpg') || imageUrl.toLowerCase().endsWith('.jpeg')) {
          image = await this.pdfDoc.embedJpg(imageBytes);
        } else {
          throw new Error(`Unsupported image format: ${imageUrl}`);
        }

        // 获取图片的原始尺寸
        const imageDims = image.scale(1);

        // 计算缩放比例以适应页面
        const scaleX = width / imageDims.width;
        const scaleY = height / imageDims.height;
        const scale = Math.min(scaleX, scaleY);

        // 计算居中位置
        const scaledWidth = imageDims.width * scale;
        const scaledHeight = imageDims.height * scale;
        const x = (width - scaledWidth) / 2;
        const y = (height - scaledHeight) / 2;

        // 绘制图片，保持宽高比并居中
        page.drawImage(image, {
          x,
          y,
          width: scaledWidth,
          height: scaledHeight,
        });

        console.log(`✅ Background image drawn successfully: ${imagePath}`);
      } else {
        throw new Error('Only relative paths starting with "/" are supported in server-side generation');
      }
    } catch (error) {
      console.error(`❌ Error drawing background image ${imagePath}:`, error);
      throw error;
    }
  }

  /**
   * 绘制边框
   */
  private async drawBorder(page: PDFPage) {
    const { border } = this.config;
    if (!border || !border.enabled) return;

    const { width, height } = this.config.dimensions;
    const borderColor = this.hexToRgb(border.color);
    
    page.drawRectangle({
      x: border.width / 2,
      y: border.width / 2,
      width: width - border.width,
      height: height - border.width,
      borderColor: borderColor,
      borderWidth: border.width
    });
  }

  /**
   * 绘制所有字段
   */
  private async drawFields(page: PDFPage) {
    for (const field of this.config.fields) {
      if (field.type === 'text') {
        await this.drawTextField(page, field);
      } else if (field.type === 'image') {
        await this.drawImageField(page, field);
      }
    }
  }

  /**
   * 绘制文本字段 - 增强版本，支持智能文本换行
   */
  private async drawTextField(page: PDFPage, field: PDFFieldConfig) {
    if (!field.text) return;

    const text = this.data[field.id] || '';
    if (!text) return;

    try {
      const font = await this.fontManager.embedFont(
        field.text.font.family,
        field.text.font.weight
      );

      const textColor = this.hexToRgb(field.text.color);
      const fontSize = field.text.font.size;
      const lineHeight = field.text.lineHeight || 1.5;

      // 特殊处理details字段，使用智能文本换行
      if (field.id === 'details' && field.dimensions) {
        await this.drawMultiLineTextField(page, field, text, font, textColor, fontSize, lineHeight);
        return;
      }

      // 单行文本字段的处理
      let x = field.position.x;
      const y = field.position.y;

      // 处理文本对齐
      if (field.text.align === 'center' && field.dimensions) {
        const textWidth = font.widthOfTextAtSize(text, fontSize);
        x = field.position.x + (field.dimensions.width - textWidth) / 2;
      } else if (field.text.align === 'right' && field.dimensions) {
        const textWidth = font.widthOfTextAtSize(text, fontSize);
        x = field.position.x + field.dimensions.width - textWidth;
      }

      page.drawText(text, {
        x,
        y,
        size: fontSize,
        font,
        color: textColor
      });

      console.log(`✅ Text field drawn: ${field.id}`);
    } catch (error) {
      console.error(`❌ Error drawing text field ${field.id}:`, error);
    }
  }

  /**
   * 绘制多行文本字段
   */
  private async drawMultiLineTextField(
    page: PDFPage,
    field: PDFFieldConfig,
    text: string,
    font: any,
    textColor: any,
    fontSize: number,
    lineHeight: number
  ) {
    if (!field.dimensions || !field.text) return;

    const maxWidth = field.dimensions.width;
    const maxHeight = field.dimensions.height;

    // 使用智能文本换行
    const lines = PDFTextUtils.wrapText(text, font, fontSize, maxWidth, maxHeight, lineHeight);
    const actualLineHeight = fontSize * lineHeight;

    // 计算起始位置
    const startX = field.position.x;
    let startY = field.position.y;

    // 如果是垂直居中，调整起始Y位置
    const totalTextHeight = lines.length * actualLineHeight;
    if (totalTextHeight < maxHeight) {
      startY = field.position.y + (maxHeight - totalTextHeight) / 2;
    }

    // 绘制每一行
    lines.forEach((line, index) => {
      if (!line.trim()) return;

      const currentY = startY - (index * actualLineHeight);

      // 计算对齐位置
      let x = startX;
      if (field.text!.align === 'center') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX + (maxWidth - lineWidth) / 2;
      } else if (field.text!.align === 'right') {
        const lineWidth = font.widthOfTextAtSize(line, fontSize);
        x = startX + maxWidth - lineWidth;
      }

      page.drawText(line, {
        x,
        y: currentY,
        size: fontSize,
        font,
        color: textColor
      });
    });

    console.log(`✅ Multi-line text field drawn: ${field.id} (${lines.length} lines)`);
  }

  /**
   * 绘制图片字段
   */
  private async drawImageField(page: PDFPage, field: PDFFieldConfig) {
    if (!field.image || !field.dimensions) return;

    try {
      // 这里可以添加图片绘制支持
      console.log(`Image field: ${field.id} - ${field.image.path}`);
    } catch (error) {
      console.error(`❌ Error drawing image field ${field.id}:`, error);
    }
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return rgb(0, 0, 0);
    }
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }
}

/**
 * 预定义的模板配置
 */
export const TEMPLATE_CONFIGS: Record<string, PDFTemplateConfig> = {
  'achievement-landscape': {
    id: 'achievement-landscape',
    name: 'Achievement Certificate (Landscape)',
    orientation: 'landscape',
    dimensions: { width: 842, height: 595 }, // A4 landscape
    background: { color: '#ffffff' },
    border: { enabled: true, color: '#2563eb', width: 3, style: 'solid' },
    fields: [
      {
        id: 'name',
        type: 'text',
        position: { x: 421, y: 350 },
        dimensions: { width: 400, height: 50 },
        text: {
          font: { family: 'Dancing Script', weight: 700, size: 36 },
          color: '#1f2937',
          align: 'center'
        }
      },
      {
        id: 'details',
        type: 'text',
        position: { x: 421, y: 280 },
        dimensions: { width: 600, height: 40 },
        text: {
          font: { family: 'Open Sans', weight: 400, size: 16 },
          color: '#374151',
          align: 'center'
        }
      },
      {
        id: 'date',
        type: 'text',
        position: { x: 150, y: 100 },
        dimensions: { width: 200, height: 30 },
        text: {
          font: { family: 'Open Sans', weight: 400, size: 14 },
          color: '#6b7280',
          align: 'left'
        }
      },
      {
        id: 'signature',
        type: 'text',
        position: { x: 492, y: 100 },
        dimensions: { width: 200, height: 30 },
        text: {
          font: { family: 'Dancing Script', weight: 500, size: 18 },
          color: '#6b7280',
          align: 'right'
        }
      }
    ]
  }
};
