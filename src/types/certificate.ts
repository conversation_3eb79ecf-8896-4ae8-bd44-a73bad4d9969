/**
 * 证书模板类型定义
 */

export interface FixedPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  align: 'left' | 'center' | 'right';
  fontSize: number;
  fontFamily: string;
  color: string;
  fontWeight?: number;
}

export interface FontConfig {
  family: string;
  size: number;
  weight: number;
  color: string;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  border?: string;
}

export interface CertificateTemplate {
  // Basic information
  id: string;
  name: string;
  displayName: string;
  description: string;

  // Category information
  category: CertificateCategory;
  tags: string[];

  // Availability status
  status: 'open' | 'closed' | 'coming-soon';

  // 视觉属性
  preview: string;
  backgroundImage?: string; // 可选的背景图片路径
  orientation: 'portrait' | 'landscape';
  aspectRatio: number; // 宽高比，用于预览和PDF生成

  // SEO属性
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  style: {
    background: string;
    border: string;
    colors: ColorScheme;
    fonts: {
      name: FontConfig;
      body: FontConfig;
      signature: FontConfig;
    };
  };
  layout: {
    name: FixedPosition;
    date: FixedPosition;
    signature: FixedPosition;
    details: FixedPosition;
  };
  constraints: {
    nameMaxLength: number;
    nameMinLength: number;
    dateMaxLength: number;
    dateMinLength: number;
    signatureMaxLength: number;
    signatureMinLength: number;
    detailsMaxLength: number;
    detailsMinLength: number;
  };
  validation: {
    namePattern: RegExp;
    datePattern: RegExp;
    signaturePattern: RegExp;
    detailsPattern: RegExp;
  };
}

export interface CertificateData {
  templateId: string;
  recipientName: string;
  date: string;
  signature: string;
  details: string;
}

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  field?: keyof CertificateData;
}

// 批量生成相关类型定义
export interface BatchCertificateData {
  recipientName: string;
  date: string;
  signature: string;
  details: string;
  // 可选的行号，用于错误追踪
  rowIndex?: number;
}

export interface BatchGenerationRequest {
  templateId: string;
  certificates: BatchCertificateData[];
  options?: {
    // 是否并行生成
    parallel?: boolean;
    // 最大并发数
    maxConcurrency?: number;
    // 输出格式选项
    outputFormat?: 'zip' | 'individual';
  };
}

export interface BatchGenerationTask {
  id: string;
  templateId: string;
  totalCount: number;
  completedCount: number;
  failedCount: number;
  status: BatchTaskStatus;
  createdAt: Date;
  updatedAt: Date;
  downloadUrl?: string;
  errors: BatchGenerationError[];
}

export enum BatchTaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface BatchGenerationError {
  rowIndex: number;
  field?: string;
  message: string;
  certificateData: BatchCertificateData;
}

export interface BatchGenerationProgress {
  taskId: string;
  status: BatchTaskStatus;
  totalCount: number;
  completedCount: number;
  failedCount: number;
  progress: number; // 0-100
  currentItem?: string;
  estimatedTimeRemaining?: number;
  errors: BatchGenerationError[];
}

export interface BatchGenerationResult {
  taskId: string;
  status: BatchTaskStatus;
  totalCount: number;
  successCount: number;
  failedCount: number;
  downloadUrl?: string;
  errors: BatchGenerationError[];
  generatedFiles: string[];
}

// 文件上传相关类型
export interface FileUploadResult {
  success: boolean;
  data?: BatchCertificateData[];
  errors?: FileParseError[];
  totalRows?: number;
  validRows?: number;
  fileName?: string;
}

export interface FileParseError {
  row: number;
  column?: string;
  message: string;
  value?: string;
}

// CSV/Excel 列映射配置
export interface ColumnMapping {
  recipientName: string;
  date: string;
  signature: string;
  details: string;
}

export interface FileParseOptions {
  hasHeader?: boolean;
  columnMapping?: ColumnMapping;
  skipEmptyRows?: boolean;
  maxRows?: number;
}

export interface FormFieldConfig {
  name: keyof CertificateData;
  label: string;
  placeholder: string;
  type: 'text' | 'date' | 'textarea';
  maxLength: number;
  minLength: number;
  required: boolean;
  validation: RegExp;
  errorMessage: string;
  mobileKeyboard?: 'default' | 'numeric' | 'email' | 'tel';
}

export interface CertificateGenerationOptions {
  template: CertificateTemplate;
  data: CertificateData;
  quality: 'standard' | 'high';
  format: 'pdf';
}

export interface GenerationProgress {
  stage: 'preparing' | 'rendering' | 'generating' | 'complete' | 'error';
  progress: number;
  message: string;
}

export interface CertificatePreview {
  templateId: string;
  previewUrl: string;
  timestamp: number;
}

// 证书分类枚举
export enum CertificateCategory {
  ACHIEVEMENT = 'achievement',           // 成就证书
  COMPLETION = 'completion',            // 完成证书
  PARTICIPATION = 'participation',      // 参与证书
  EXCELLENCE = 'excellence',            // 优秀证书
  CUSTOM = 'custom'                     // 自定义证书
}

// 模板分类配置接口
export interface TemplateCategory {
  id: CertificateCategory;
  name: string;
  displayName: string;
  description: string;
  seoKeywords: string[];
  urlSlug: string;
  metaTitle: string;
  metaDescription: string;
  templates: CertificateTemplate[];
  defaultSize: 'portrait' | 'landscape';
  defaultTemplate: string;
  // 新增字段
  previewImage?: string; // 分类预览图片
  templateCount?: number; // 模板数量
  enabled?: boolean; // 是否启用该分类，默认为true
}





// 错误类型
export interface CertificateError {
  code: string;
  message: string;
  field?: keyof CertificateData;
  details?: any;
}

// 分析事件类型
export interface AnalyticsEvent {
  event: string;
  category: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

// 用户偏好设置
export interface UserPreferences {
  preferredTemplate?: string;
  autoSave: boolean;
  showPreview: boolean;
  mobileOptimized: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// 本地存储数据结构
export interface LocalStorageData {
  formData?: Partial<CertificateData>;
  selectedTemplate?: string;
  preferences?: UserPreferences;
  lastUsed?: number;
}

// API响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: CertificateError;
  timestamp: number;
}

// 性能指标
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  generationTime: number;
  downloadTime: number;
  totalTime: number;
}

// 设备信息
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  userAgent: string;
  touchSupport: boolean;
}
