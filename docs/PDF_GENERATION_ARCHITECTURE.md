# PDF生成架构说明

## 当前实现方式

### 单个证书生成
- **位置**: 浏览器端 (`src/lib/pdf-generator.ts`)
- **方式**: 使用 `pdf-lib` 在客户端直接生成PDF
- **下载**: 通过 `URL.createObjectURL()` 创建临时链接，直接在浏览器中下载
- **优点**: 
  - 不占用服务器资源
  - 响应速度快
  - 不需要文件存储

### 批量证书生成
- **位置**: 服务器端 (`src/lib/batch-pdf-generator.ts`)
- **方式**: 在服务器的 `temp` 文件夹中生成PDF文件，然后打包为ZIP
- **下载**: 通过API端点 `/api/batch/download/[taskId]` 提供下载
- **原因**:
  1. **内存限制**: 浏览器无法同时处理大量PDF生成（会导致内存溢出）
  2. **并发控制**: 服务器端可以控制并发数量，避免系统过载
  3. **稳定性**: 长时间运行的任务在服务器端更稳定
  4. **进度跟踪**: 可以实时跟踪批量生成进度

## 为什么使用temp文件夹

### 技术原因
1. **内存管理**: 直接在内存中生成50个PDF文件会占用大量内存（每个PDF约1-2MB）
2. **流式处理**: 文件系统允许流式写入，避免内存峰值
3. **错误恢复**: 如果生成过程中出错，已生成的文件不会丢失
4. **ZIP打包**: 需要将多个PDF文件打包为ZIP，文件系统操作更高效

### 存储策略
- **临时存储**: 文件存储在 `temp/batch-pdfs/[taskId]/` 目录
- **自动清理**: 任务完成后2小时自动清理文件
- **下载后清理**: 用户下载后标记任务，定期清理

## 优化建议

### 短期优化（已实现）
1. ✅ 限制批量生成数量为50个
2. ✅ 优化按钮样式，移除"Bulk"标识
3. ✅ 改进错误提示信息

### 中期优化（可考虑）
1. **流式ZIP生成**: 边生成PDF边添加到ZIP，减少磁盘占用
2. **内存缓存**: 对于小批量（<10个），可以考虑纯内存操作
3. **CDN集成**: 将生成的文件上传到CDN，提供更快的下载速度

### 长期优化（架构改进）
1. **队列系统**: 使用Redis或数据库队列管理批量任务
2. **分布式处理**: 多服务器并行处理大批量任务
3. **WebSocket实时更新**: 实时推送生成进度给用户

## 当前限制说明

### 批量生成限制
- **最大数量**: 50个证书/批次
- **文件大小**: 单个文件最大10MB
- **超时时间**: 30分钟任务超时
- **并发控制**: 最多8个并发生成任务

### 为什么不能直接在浏览器生成批量PDF

1. **浏览器内存限制**: 
   - 现代浏览器单标签页内存限制约2GB
   - 50个PDF文件约需要100-200MB内存
   - 加上生成过程中的临时对象，容易导致内存溢出

2. **UI阻塞**:
   - PDF生成是CPU密集型操作
   - 在主线程中生成会导致页面卡死
   - Web Worker虽然可以解决，但仍有内存限制

3. **错误处理**:
   - 浏览器崩溃会丢失所有已生成的文件
   - 服务器端可以实现断点续传和错误恢复

4. **用户体验**:
   - 用户可能在生成过程中关闭页面
   - 服务器端生成允许用户稍后返回下载

## 结论

当前的架构设计是合理的：
- **单个证书**: 浏览器端生成，快速响应
- **批量证书**: 服务器端生成，稳定可靠

temp文件夹的使用是必要的技术选择，确保了系统的稳定性和可扩展性。通过合理的清理策略，可以有效管理存储空间。
